const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const AdLog = sequelize.define('AdLog', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
      comment: '广告记录ID'
    },
    userId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '用户ID'
    },
    adType: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '广告类型'
    },
    points: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '获得积分'
    },
    watchTime: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: '观看时长（秒）'
    },
    ip: {
      type: DataTypes.STRING(45),
      allowNull: true,
      comment: '观看IP'
    }
  }, {
    tableName: 'ad_logs',
    comment: '广告观看记录表',
    indexes: [
      {
        fields: ['user_id', 'created_at']
      },
      {
        fields: ['ad_type']
      }
    ]
  });

  return AdLog;
};
