const Router = require('koa-router');
const authController = require('../controllers/authController');
const { loginRateLimit } = require('../middleware/rateLimiter');

const router = new Router();

// 用户注册
router.post('/register', authController.register);

// 用户登录（手机号+验证码）
router.post('/login', loginRateLimit, authController.login);

// 微信登录
router.post('/wechat-login', authController.wechatLogin);

// 发送验证码
router.post('/send-sms', authController.sendSms);

// 刷新令牌
router.post('/refresh', authController.refreshToken);

// 用户登出
router.post('/logout', authController.logout);

// 验证令牌
router.get('/verify', authController.verifyToken);

module.exports = router;
