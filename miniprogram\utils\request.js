const app = getApp();

// 请求基础配置
const config = {
  baseUrl: 'http://localhost:3000/api/v1',
  timeout: 10000
};

// 请求拦截器
function requestInterceptor(options) {
  // 添加token
  const token = app.getToken();
  if (token) {
    options.header = {
      ...options.header,
      'Authorization': `Bearer ${token}`
    };
  }
  
  // 添加基础header
  options.header = {
    'Content-Type': 'application/json',
    ...options.header
  };
  
  return options;
}

// 响应拦截器
function responseInterceptor(response, resolve, reject) {
  const { statusCode, data } = response;
  
  // HTTP状态码检查
  if (statusCode >= 200 && statusCode < 300) {
    // 业务状态码检查
    if (data.code === 200) {
      resolve(data);
    } else if (data.code === 401) {
      // token过期，重新登录
      app.logout();
      reject(new Error('登录已过期，请重新登录'));
    } else {
      reject(new Error(data.message || '请求失败'));
    }
  } else {
    reject(new Error(`网络错误: ${statusCode}`));
  }
}

// 基础请求方法
function request(options) {
  return new Promise((resolve, reject) => {
    // 请求拦截
    const requestOptions = requestInterceptor({
      url: config.baseUrl + options.url,
      method: options.method || 'GET',
      data: options.data,
      header: options.header || {},
      timeout: options.timeout || config.timeout
    });
    
    wx.request({
      ...requestOptions,
      success: (response) => {
        responseInterceptor(response, resolve, reject);
      },
      fail: (error) => {
        console.error('请求失败', error);
        reject(new Error('网络请求失败'));
      }
    });
  });
}

// GET请求
function get(url, params = {}) {
  // 处理查询参数
  const queryString = Object.keys(params)
    .filter(key => params[key] !== undefined && params[key] !== null)
    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
    .join('&');
  
  const fullUrl = queryString ? `${url}?${queryString}` : url;
  
  return request({
    url: fullUrl,
    method: 'GET'
  });
}

// POST请求
function post(url, data = {}) {
  return request({
    url,
    method: 'POST',
    data
  });
}

// PUT请求
function put(url, data = {}) {
  return request({
    url,
    method: 'PUT',
    data
  });
}

// DELETE请求
function del(url, data = {}) {
  return request({
    url,
    method: 'DELETE',
    data
  });
}

// 文件上传
function upload(url, filePath, formData = {}) {
  return new Promise((resolve, reject) => {
    const token = app.getToken();
    const header = {};
    
    if (token) {
      header['Authorization'] = `Bearer ${token}`;
    }
    
    wx.uploadFile({
      url: config.baseUrl + url,
      filePath,
      name: 'file',
      formData,
      header,
      success: (response) => {
        try {
          const data = JSON.parse(response.data);
          if (data.code === 200) {
            resolve(data);
          } else {
            reject(new Error(data.message || '上传失败'));
          }
        } catch (error) {
          reject(new Error('响应数据格式错误'));
        }
      },
      fail: (error) => {
        console.error('上传失败', error);
        reject(new Error('文件上传失败'));
      }
    });
  });
}

// 下载文件
function download(url, params = {}) {
  return new Promise((resolve, reject) => {
    const queryString = Object.keys(params)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
      .join('&');
    
    const fullUrl = queryString ? `${config.baseUrl}${url}?${queryString}` : `${config.baseUrl}${url}`;
    
    wx.downloadFile({
      url: fullUrl,
      success: (response) => {
        if (response.statusCode === 200) {
          resolve(response);
        } else {
          reject(new Error('下载失败'));
        }
      },
      fail: (error) => {
        console.error('下载失败', error);
        reject(new Error('文件下载失败'));
      }
    });
  });
}

module.exports = {
  request,
  get,
  post,
  put,
  delete: del,
  upload,
  download,
  config
};
