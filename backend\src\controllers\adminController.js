const jwt = require('jsonwebtoken');
const { Admin, AdminRole, User, Content, PointConfig, PointLog } = require('../models');
const config = require('../config/config');
const logger = require('../utils/logger');
const { BusinessError, ValidationError, NotFoundError } = require('../middleware/errorHandler');

class AdminController {
  // 管理员登录
  async login(ctx) {
    const { username, password } = ctx.request.body;

    if (!username || !password) {
      throw new ValidationError('用户名和密码不能为空');
    }

    // 查找管理员
    const admin = await Admin.findOne({
      where: { username },
      include: [
        {
          model: AdminRole,
          as: 'role',
          attributes: ['id', 'name', 'permissions']
        }
      ]
    });

    if (!admin) {
      throw new BusinessError('用户名或密码错误');
    }

    if (admin.status !== 1) {
      throw new BusinessError('账号已被禁用');
    }

    // 验证密码
    const isValidPassword = await admin.validatePassword(password);
    if (!isValidPassword) {
      throw new BusinessError('用户名或密码错误');
    }

    // 更新登录信息
    admin.lastLoginTime = new Date();
    admin.lastLoginIp = ctx.ip;
    await admin.save();

    // 生成JWT令牌
    const token = jwt.sign(
      {
        adminId: admin.id,
        username: admin.username,
        roleId: admin.roleId
      },
      config.jwt.secret,
      { expiresIn: config.jwt.expiresIn }
    );

    logger.business('管理员登录', admin.id, { username, ip: ctx.ip });

    ctx.body = {
      code: 200,
      message: '登录成功',
      data: {
        admin: admin.toJSON(),
        token
      }
    };
  }

  // 获取管理员信息
  async getProfile(ctx) {
    const admin = ctx.state.admin;

    ctx.body = {
      code: 200,
      message: '获取成功',
      data: admin.toJSON()
    };
  }

  // 更新管理员信息
  async updateProfile(ctx) {
    const admin = ctx.state.admin;
    const { nickname, email, avatar } = ctx.request.body;

    if (nickname) admin.nickname = nickname;
    if (email) admin.email = email;
    if (avatar) admin.avatar = avatar;

    await admin.save();

    logger.business('更新管理员信息', admin.id, { nickname, email });

    ctx.body = {
      code: 200,
      message: '更新成功',
      data: admin.toJSON()
    };
  }

  // 获取用户列表
  async getUsers(ctx) {
    const { 
      page = 1, 
      pageSize = 20, 
      keyword, 
      status, 
      vipLevel,
      startDate,
      endDate 
    } = ctx.query;

    const offset = (parseInt(page) - 1) * parseInt(pageSize);
    const where = {};

    // 关键词搜索
    if (keyword) {
      const { Op } = require('sequelize');
      where[Op.or] = [
        { nickname: { [Op.like]: `%${keyword}%` } },
        { mobile: { [Op.like]: `%${keyword}%` } }
      ];
    }

    // 状态筛选
    if (status) {
      where.status = status;
    }

    // VIP等级筛选
    if (vipLevel) {
      where.vipLevel = vipLevel;
    }

    // 时间范围筛选
    if (startDate && endDate) {
      const { Op } = require('sequelize');
      where.createdAt = {
        [Op.between]: [new Date(startDate), new Date(endDate)]
      };
    }

    const result = await User.findAndCountAll({
      where,
      order: [['createdAt', 'DESC']],
      limit: parseInt(pageSize),
      offset,
      attributes: {
        exclude: ['remark'] // 排除敏感字段
      }
    });

    ctx.body = {
      code: 200,
      message: '获取成功',
      data: {
        list: result.rows,
        total: result.count,
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      }
    };
  }

  // 获取用户详情
  async getUserDetail(ctx) {
    const { id } = ctx.params;

    const user = await User.findByPk(id, {
      include: [
        {
          model: User,
          as: 'inviter',
          attributes: ['id', 'nickname', 'mobile']
        }
      ]
    });

    if (!user) {
      throw new NotFoundError('用户不存在');
    }

    // 获取用户统计信息
    const [unlockCount, favoriteCount, commentCount] = await Promise.all([
      require('../models').ContentUnlock.count({ where: { userId: id } }),
      require('../models').Favorite.count({ where: { userId: id } }),
      require('../models').Comment.count({ where: { userId: id } })
    ]);

    const userDetail = user.toJSON();
    userDetail.stats = {
      unlockCount,
      favoriteCount,
      commentCount
    };

    ctx.body = {
      code: 200,
      message: '获取成功',
      data: userDetail
    };
  }

  // 更新用户信息
  async updateUser(ctx) {
    const { id } = ctx.params;
    const { nickname, status, vipLevel, vipExpireTime, remark } = ctx.request.body;

    const user = await User.findByPk(id);
    if (!user) {
      throw new NotFoundError('用户不存在');
    }

    // 更新字段
    if (nickname) user.nickname = nickname;
    if (status !== undefined) user.status = status;
    if (vipLevel !== undefined) user.vipLevel = vipLevel;
    if (vipExpireTime) user.vipExpireTime = new Date(vipExpireTime);
    if (remark !== undefined) user.remark = remark;

    await user.save();

    logger.business('更新用户信息', ctx.state.admin.id, { 
      targetUserId: id, 
      changes: { nickname, status, vipLevel, vipExpireTime } 
    });

    ctx.body = {
      code: 200,
      message: '更新成功',
      data: user.toJSON()
    };
  }

  // 调整用户积分
  async adjustUserPoints(ctx) {
    const { id } = ctx.params;
    const { amount, description } = ctx.request.body;

    if (!amount || amount === 0) {
      throw new ValidationError('积分调整数量不能为空或为0');
    }

    const user = await User.findByPk(id);
    if (!user) {
      throw new NotFoundError('用户不存在');
    }

    // 调整积分
    if (amount > 0) {
      await user.addPoints(amount, description || '管理员调整');
    } else {
      await user.deductPoints(Math.abs(amount), description || '管理员调整');
    }

    // 记录管理员操作
    await PointLog.createLog({
      userId: id,
      type: 'admin_adjust',
      source: 'admin_adjust',
      amount,
      balance: user.points,
      description: description || '管理员调整',
      adminId: ctx.state.admin.id,
      ip: ctx.ip
    });

    logger.business('调整用户积分', ctx.state.admin.id, { 
      targetUserId: id, 
      amount, 
      description 
    });

    ctx.body = {
      code: 200,
      message: '调整成功',
      data: {
        userId: id,
        amount,
        newBalance: user.points
      }
    };
  }

  // 设置用户VIP
  async setUserVip(ctx) {
    const { id } = ctx.params;
    const { vipLevel, days } = ctx.request.body;

    if (!vipLevel || !days) {
      throw new ValidationError('VIP等级和天数不能为空');
    }

    const user = await User.findByPk(id);
    if (!user) {
      throw new NotFoundError('用户不存在');
    }

    // 设置VIP
    const expireTime = new Date();
    if (user.vipExpireTime && user.vipExpireTime > new Date()) {
      expireTime.setTime(user.vipExpireTime.getTime());
    }
    expireTime.setDate(expireTime.getDate() + parseInt(days));

    user.vipLevel = Math.max(user.vipLevel, parseInt(vipLevel));
    user.vipExpireTime = expireTime;
    await user.save();

    logger.business('设置用户VIP', ctx.state.admin.id, { 
      targetUserId: id, 
      vipLevel, 
      days 
    });

    ctx.body = {
      code: 200,
      message: '设置成功',
      data: {
        userId: id,
        vipLevel: user.vipLevel,
        vipExpireTime: user.vipExpireTime
      }
    };
  }

  // 封禁用户
  async banUser(ctx) {
    const { id } = ctx.params;
    const { reason } = ctx.request.body;

    const user = await User.findByPk(id);
    if (!user) {
      throw new NotFoundError('用户不存在');
    }

    user.status = 2; // 封禁状态
    user.remark = reason || '违规封禁';
    await user.save();

    logger.business('封禁用户', ctx.state.admin.id, { 
      targetUserId: id, 
      reason 
    });

    ctx.body = {
      code: 200,
      message: '封禁成功'
    };
  }

  // 获取积分配置
  async getPointConfig(ctx) {
    const configs = await PointConfig.findAll({
      order: [['configType', 'ASC'], ['configKey', 'ASC']]
    });

    // 按类型分组
    const groupedConfigs = {
      gain: [],
      consume: [],
      limit: []
    };

    configs.forEach(config => {
      const configData = config.toJSON();
      if (groupedConfigs[config.configType]) {
        groupedConfigs[config.configType].push(configData);
      }
    });

    ctx.body = {
      code: 200,
      message: '获取成功',
      data: groupedConfigs
    };
  }

  // 更新积分配置
  async updatePointConfig(ctx) {
    const { key } = ctx.params;
    const { value, description } = ctx.request.body;

    if (!value) {
      throw new ValidationError('配置值不能为空');
    }

    const config = await PointConfig.findOne({
      where: { configKey: key }
    });

    if (!config) {
      throw new NotFoundError('配置不存在');
    }

    // 更新配置
    config.setValue(value);
    if (description) config.description = description;
    config.updatedBy = ctx.state.admin.id;
    await config.save();

    logger.business('更新积分配置', ctx.state.admin.id, { 
      configKey: key, 
      value, 
      description 
    });

    ctx.body = {
      code: 200,
      message: '更新成功',
      data: config.toJSON()
    };
  }

  // 批量更新积分配置
  async batchUpdatePointConfig(ctx) {
    const { configs } = ctx.request.body;

    if (!configs || !Array.isArray(configs)) {
      throw new ValidationError('配置数据格式错误');
    }

    const results = await PointConfig.setBatchConfigs(
      configs.map(item => ({
        key: item.key,
        value: item.value,
        type: item.type,
        description: item.description
      })),
      ctx.state.admin.id
    );

    logger.business('批量更新积分配置', ctx.state.admin.id, { 
      count: configs.length 
    });

    ctx.body = {
      code: 200,
      message: '批量更新成功',
      data: results
    };
  }

  // 获取概览统计
  async getOverviewStats(ctx) {
    const { days = 7 } = ctx.query;
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(days));

    const [
      totalUsers,
      totalContents,
      totalPoints,
      newUsers,
      newContents,
      pointsConsumed
    ] = await Promise.all([
      User.count({ where: { status: 1 } }),
      Content.count({ where: { status: 1 } }),
      PointLog.sum('amount', { where: { type: 'gain' } }) || 0,
      User.count({
        where: {
          createdAt: { [require('sequelize').Op.gte]: startDate }
        }
      }),
      Content.count({
        where: {
          createdAt: { [require('sequelize').Op.gte]: startDate }
        }
      }),
      Math.abs(PointLog.sum('amount', {
        where: {
          type: 'consume',
          createdAt: { [require('sequelize').Op.gte]: startDate }
        }
      }) || 0)
    ]);

    ctx.body = {
      code: 200,
      message: '获取成功',
      data: {
        totalUsers,
        totalContents,
        totalPoints,
        newUsers,
        newContents,
        pointsConsumed,
        period: `${days}天`
      }
    };
  }
}

module.exports = new AdminController();
