<template>
  <div id="app" class="app-container">
    <!-- 全局加载条 -->
    <div v-if="loading" class="global-loading">
      <el-loading-service />
    </div>

    <!-- 主要内容 -->
    <router-view v-slot="{ Component, route }">
      <transition name="fade" mode="out-in">
        <component :is="Component" :key="route.path" />
      </transition>
    </router-view>

    <!-- 全局消息提示 -->
    <el-backtop :right="40" :bottom="40" />
    
    <!-- 微信分享配置 -->
    <div v-if="isWechat" style="display: none;">
      <div id="wechat-share-config" :data-config="wechatShareConfig"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useSystemStore } from '@/stores/system'
import { isWechatBrowser, configWechatShare } from '@/utils/wechat'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()
const systemStore = useSystemStore()

const loading = ref(false)
const isWechat = ref(false)
const wechatShareConfig = ref({})

onMounted(async () => {
  // 检测是否在微信环境
  isWechat.value = isWechatBrowser()
  
  // 初始化应用
  await initApp()
  
  // 配置微信分享
  if (isWechat.value) {
    setupWechatShare()
  }
})

// 初始化应用
async function initApp() {
  try {
    loading.value = true
    
    // 加载系统配置
    await systemStore.loadConfig()
    
    // 检查登录状态
    await userStore.checkLoginStatus()
    
    // 处理URL参数
    handleUrlParams()
    
  } catch (error) {
    console.error('应用初始化失败:', error)
  } finally {
    loading.value = false
  }
}

// 处理URL参数
function handleUrlParams() {
  const { query } = route
  
  // 处理邀请码
  if (query.inviteCode) {
    localStorage.setItem('inviteCode', query.inviteCode)
  }
  
  // 处理分享来源
  if (query.from) {
    localStorage.setItem('shareFrom', query.from)
  }
}

// 设置微信分享
function setupWechatShare() {
  const defaultConfig = {
    title: '知识付费平台 - 优质学习资源分享',
    desc: '专业的知识付费学习平台，提供考研、公务员、技能提升等优质内容',
    link: window.location.href,
    imgUrl: `${window.location.origin}/images/share-default.jpg`
  }
  
  wechatShareConfig.value = defaultConfig
  configWechatShare(defaultConfig)
}

// 监听路由变化，更新分享配置
watch(route, (newRoute) => {
  if (isWechat.value) {
    updateWechatShare(newRoute)
  }
}, { immediate: true })

// 更新微信分享配置
function updateWechatShare(route) {
  let shareConfig = {
    title: '知识付费平台',
    desc: '优质学习资源分享平台',
    link: window.location.href,
    imgUrl: `${window.location.origin}/images/share-default.jpg`
  }
  
  // 根据页面类型自定义分享内容
  if (route.name === 'ContentDetail') {
    // 内容详情页分享
    shareConfig.title = route.meta.title || '精彩内容分享'
    shareConfig.desc = route.meta.description || '来看看这个有趣的内容'
  } else if (route.name === 'Invite') {
    // 邀请页面分享
    shareConfig.title = '邀请你加入知识付费平台'
    shareConfig.desc = '注册即可获得积分奖励，海量优质内容等你来'
  }
  
  // 添加邀请参数
  const userInfo = userStore.userInfo
  if (userInfo && userInfo.inviteCode) {
    const url = new URL(shareConfig.link)
    url.searchParams.set('inviteCode', userInfo.inviteCode)
    shareConfig.link = url.toString()
  }
  
  wechatShareConfig.value = shareConfig
  configWechatShare(shareConfig)
}

// 全局错误处理
window.addEventListener('error', (event) => {
  console.error('全局错误:', event.error)
})

window.addEventListener('unhandledrejection', (event) => {
  console.error('未处理的Promise拒绝:', event.reason)
})
</script>

<style lang="scss">
.app-container {
  min-height: 100vh;
  background-color: var(--el-bg-color-page);
}

.global-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

// 路由过渡动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

// 响应式设计
@media (max-width: 768px) {
  .app-container {
    padding: 0;
  }
}
</style>
