import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

// 配置NProgress
NProgress.configure({ 
  showSpinner: false,
  minimum: 0.2,
  speed: 500
})

// 路由配置
const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/Home.vue'),
    meta: {
      title: '首页',
      keepAlive: true
    }
  },
  {
    path: '/category',
    name: 'Category',
    component: () => import('@/views/Category.vue'),
    meta: {
      title: '分类'
    }
  },
  {
    path: '/content/:id',
    name: 'ContentDetail',
    component: () => import('@/views/ContentDetail.vue'),
    meta: {
      title: '内容详情'
    }
  },
  {
    path: '/search',
    name: 'Search',
    component: () => import('@/views/Search.vue'),
    meta: {
      title: '搜索'
    }
  },
  {
    path: '/user',
    name: 'User',
    component: () => import('@/views/User/Index.vue'),
    meta: {
      title: '个人中心',
      requiresAuth: true
    },
    children: [
      {
        path: '',
        name: 'UserProfile',
        component: () => import('@/views/User/Profile.vue'),
        meta: {
          title: '个人资料'
        }
      },
      {
        path: 'points',
        name: 'UserPoints',
        component: () => import('@/views/User/Points.vue'),
        meta: {
          title: '我的积分'
        }
      },
      {
        path: 'favorites',
        name: 'UserFavorites',
        component: () => import('@/views/User/Favorites.vue'),
        meta: {
          title: '我的收藏'
        }
      },
      {
        path: 'unlocks',
        name: 'UserUnlocks',
        component: () => import('@/views/User/Unlocks.vue'),
        meta: {
          title: '解锁记录'
        }
      },
      {
        path: 'invite',
        name: 'UserInvite',
        component: () => import('@/views/User/Invite.vue'),
        meta: {
          title: '邀请好友'
        }
      }
    ]
  },
  {
    path: '/auth/login',
    name: 'Login',
    component: () => import('@/views/Auth/Login.vue'),
    meta: {
      title: '登录',
      hideForAuth: true
    }
  },
  {
    path: '/auth/register',
    name: 'Register',
    component: () => import('@/views/Auth/Register.vue'),
    meta: {
      title: '注册',
      hideForAuth: true
    }
  },
  {
    path: '/point/sign',
    name: 'PointSign',
    component: () => import('@/views/Point/Sign.vue'),
    meta: {
      title: '每日签到',
      requiresAuth: true
    }
  },
  {
    path: '/point/exchange',
    name: 'PointExchange',
    component: () => import('@/views/Point/Exchange.vue'),
    meta: {
      title: '卡密兑换',
      requiresAuth: true
    }
  },
  {
    path: '/invite/:code?',
    name: 'Invite',
    component: () => import('@/views/Invite.vue'),
    meta: {
      title: '邀请注册'
    }
  },
  {
    path: '/about',
    name: 'About',
    component: () => import('@/views/About.vue'),
    meta: {
      title: '关于我们'
    }
  },
  {
    path: '/privacy',
    name: 'Privacy',
    component: () => import('@/views/Privacy.vue'),
    meta: {
      title: '隐私政策'
    }
  },
  {
    path: '/terms',
    name: 'Terms',
    component: () => import('@/views/Terms.vue'),
    meta: {
      title: '服务条款'
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue'),
    meta: {
      title: '页面不存在'
    }
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 全局前置守卫
router.beforeEach(async (to, from, next) => {
  // 开始进度条
  NProgress.start()
  
  // 设置页面标题
  const title = to.meta.title
  if (title) {
    document.title = `${title} - 知识付费平台`
  }
  
  const userStore = useUserStore()
  
  // 检查是否需要登录
  if (to.meta.requiresAuth) {
    if (!userStore.isLoggedIn) {
      // 保存目标路由，登录后跳转
      sessionStorage.setItem('redirectPath', to.fullPath)
      next('/auth/login')
      return
    }
  }
  
  // 已登录用户访问登录/注册页面，重定向到首页
  if (to.meta.hideForAuth && userStore.isLoggedIn) {
    next('/')
    return
  }
  
  next()
})

// 全局后置守卫
router.afterEach((to, from) => {
  // 结束进度条
  NProgress.done()
  
  // 页面访问统计
  if (typeof gtag !== 'undefined') {
    gtag('config', 'GA_MEASUREMENT_ID', {
      page_path: to.path
    })
  }
})

// 路由错误处理
router.onError((error) => {
  console.error('路由错误:', error)
  NProgress.done()
})

export default router
