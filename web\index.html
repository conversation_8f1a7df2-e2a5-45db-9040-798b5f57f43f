<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="知识付费平台 - 优质学习资源分享平台" />
    <meta name="keywords" content="知识付费,学习资源,在线教育,考研,公务员,技能提升" />
    <title>知识付费平台</title>
    
    <!-- 微信分享配置 -->
    <meta property="og:title" content="知识付费平台" />
    <meta property="og:description" content="优质学习资源分享平台" />
    <meta property="og:image" content="/images/share-default.jpg" />
    <meta property="og:url" content="https://your-domain.com" />
    
    <!-- 移动端适配 -->
    <meta name="format-detection" content="telephone=no" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    
    <!-- 预加载关键资源 -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://cdn.jsdelivr.net" />
    
    <!-- 样式预加载 -->
    <style>
      /* 加载动画 */
      .app-loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid #f3f3f3;
        border-top: 3px solid #1890ff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      .loading-text {
        margin-top: 16px;
        color: #666;
        font-size: 14px;
      }
    </style>
  </head>
  <body>
    <div id="app">
      <!-- 初始加载动画 -->
      <div class="app-loading">
        <div>
          <div class="loading-spinner"></div>
          <div class="loading-text">加载中...</div>
        </div>
      </div>
    </div>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>
