const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const PointConfig = sequelize.define('PointConfig', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      comment: '配置ID'
    },
    configKey: {
      type: DataTypes.STRING(50),
      unique: true,
      allowNull: false,
      comment: '配置键名'
    },
    configValue: {
      type: DataTypes.TEXT,
      allowNull: false,
      comment: '配置值（JSON格式）'
    },
    configType: {
      type: DataTypes.STRING(20),
      allowNull: false,
      comment: '配置类型：gain获取/consume消耗/limit限制'
    },
    description: {
      type: DataTypes.STRING(200),
      allowNull: true,
      comment: '配置说明'
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: '是否启用'
    },
    createdBy: {
      type: DataTypes.BIGINT,
      allowNull: true,
      comment: '创建人'
    },
    updatedBy: {
      type: DataTypes.BIGINT,
      allowNull: true,
      comment: '更新人'
    }
  }, {
    tableName: 'point_config',
    comment: '积分配置表',
    indexes: [
      {
        unique: true,
        fields: ['config_key']
      },
      {
        fields: ['config_type']
      },
      {
        fields: ['is_active']
      }
    ]
  });

  // 实例方法
  PointConfig.prototype.toJSON = function() {
    const values = { ...this.get() };
    // 解析JSON配置值
    try {
      values.configValue = JSON.parse(values.configValue);
    } catch (error) {
      // 如果解析失败，保持原值
    }
    return values;
  };

  // 获取解析后的配置值
  PointConfig.prototype.getParsedValue = function() {
    try {
      return JSON.parse(this.configValue);
    } catch (error) {
      return this.configValue;
    }
  };

  // 设置配置值（自动JSON序列化）
  PointConfig.prototype.setValue = function(value) {
    if (typeof value === 'object') {
      this.configValue = JSON.stringify(value);
    } else {
      this.configValue = value;
    }
  };

  // 类方法
  // 根据配置键获取配置
  PointConfig.getByKey = async function(key) {
    const config = await this.findOne({
      where: { 
        configKey: key,
        isActive: true
      }
    });
    return config ? config.getParsedValue() : null;
  };

  // 根据类型获取所有配置
  PointConfig.getByType = async function(type) {
    const configs = await this.findAll({
      where: { 
        configType: type,
        isActive: true
      }
    });
    
    const result = {};
    configs.forEach(config => {
      result[config.configKey] = config.getParsedValue();
    });
    return result;
  };

  // 获取所有积分获取配置
  PointConfig.getGainConfigs = function() {
    return this.getByType('gain');
  };

  // 获取所有积分消耗配置
  PointConfig.getConsumeConfigs = function() {
    return this.getByType('consume');
  };

  // 获取所有限制配置
  PointConfig.getLimitConfigs = function() {
    return this.getByType('limit');
  };

  // 设置配置值
  PointConfig.setConfig = async function(key, value, type, description, updatedBy) {
    const configValue = typeof value === 'object' ? JSON.stringify(value) : value;
    
    const [config, created] = await this.findOrCreate({
      where: { configKey: key },
      defaults: {
        configKey: key,
        configValue,
        configType: type,
        description,
        createdBy: updatedBy,
        updatedBy: updatedBy
      }
    });

    if (!created) {
      config.configValue = configValue;
      config.configType = type;
      config.description = description;
      config.updatedBy = updatedBy;
      config.isActive = true;
      await config.save();
    }

    return config;
  };

  // 批量设置配置
  PointConfig.setBatchConfigs = async function(configs, updatedBy) {
    const transaction = await sequelize.transaction();
    try {
      const results = [];
      
      for (const { key, value, type, description } of configs) {
        const config = await this.setConfig(key, value, type, description, updatedBy);
        results.push(config);
      }
      
      await transaction.commit();
      return results;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  };

  // 禁用配置
  PointConfig.disableConfig = async function(key, updatedBy) {
    const config = await this.findOne({ where: { configKey: key } });
    if (config) {
      config.isActive = false;
      config.updatedBy = updatedBy;
      await config.save();
    }
    return config;
  };

  // 获取默认积分配置
  PointConfig.getDefaultConfigs = function() {
    return [
      {
        key: 'daily_sign_base',
        value: { points: 5, maxPerDay: 1 },
        type: 'gain',
        description: '每日签到基础积分'
      },
      {
        key: 'daily_sign_continuous_7',
        value: { points: 10, condition: 'continuous_7_days' },
        type: 'gain',
        description: '连续签到7天奖励'
      },
      {
        key: 'daily_sign_continuous_30',
        value: { points: 20, condition: 'continuous_30_days' },
        type: 'gain',
        description: '连续签到30天奖励'
      },
      {
        key: 'watch_ad',
        value: { points: 3, maxPerDay: 5 },
        type: 'gain',
        description: '观看广告积分'
      },
      {
        key: 'invite_register',
        value: { points: 20, maxPerDay: -1 },
        type: 'gain',
        description: '邀请注册积分'
      },
      {
        key: 'invite_unlock_ratio',
        value: { ratio: 0.2 },
        type: 'gain',
        description: '邀请解锁分成比例'
      },
      {
        key: 'share_content',
        value: { points: 2, maxPerDay: 10 },
        type: 'gain',
        description: '分享内容积分'
      },
      {
        key: 'content_creation',
        value: { points: 100, maxPerDay: -1 },
        type: 'gain',
        description: '内容创作积分'
      },
      {
        key: 'complete_profile',
        value: { points: 10, maxTimes: 1 },
        type: 'gain',
        description: '完善资料积分'
      },
      {
        key: 'first_unlock',
        value: { points: 5, maxTimes: 1 },
        type: 'gain',
        description: '首次解锁奖励'
      },
      {
        key: 'point_expire_days',
        value: { days: 365 },
        type: 'limit',
        description: '积分有效期（天）'
      },
      {
        key: 'daily_gain_limit',
        value: { maxPoints: 200 },
        type: 'limit',
        description: '每日获取积分上限'
      }
    ];
  };

  // 初始化默认配置
  PointConfig.initDefaultConfigs = async function() {
    const defaultConfigs = this.getDefaultConfigs();
    const results = [];
    
    for (const config of defaultConfigs) {
      const [pointConfig, created] = await this.findOrCreate({
        where: { configKey: config.key },
        defaults: {
          configKey: config.key,
          configValue: JSON.stringify(config.value),
          configType: config.type,
          description: config.description,
          isActive: true
        }
      });
      
      if (created) {
        results.push(pointConfig);
      }
    }
    
    return results;
  };

  return PointConfig;
};
