{"name": "zhis-web", "version": "1.0.0", "description": "微信知识付费平台网页端", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"vue": "^3.3.8", "vue-router": "^4.2.5", "pinia": "^2.1.7", "axios": "^1.6.2", "element-plus": "^2.4.4", "@element-plus/icons-vue": "^2.1.0", "nprogress": "^0.2.0", "js-cookie": "^3.0.5", "dayjs": "^1.11.10", "marked": "^11.1.1", "highlight.js": "^11.9.0", "qrcode": "^1.5.3", "vue3-lazyload": "^0.3.8"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.2", "vite": "^5.0.8", "eslint": "^8.55.0", "eslint-plugin-vue": "^9.19.2", "prettier": "^3.1.1", "@types/js-cookie": "^3.0.6", "@types/nprogress": "^0.2.3", "sass": "^1.69.5", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.25.2"}}