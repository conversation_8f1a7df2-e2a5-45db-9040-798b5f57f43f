const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const ContentLike = sequelize.define('ContentLike', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
      comment: '点赞ID'
    },
    userId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '用户ID'
    },
    contentId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '内容ID'
    }
  }, {
    tableName: 'content_likes',
    comment: '内容点赞表',
    indexes: [
      {
        unique: true,
        fields: ['user_id', 'content_id']
      },
      {
        fields: ['user_id']
      },
      {
        fields: ['content_id']
      }
    ]
  });

  return ContentLike;
};
