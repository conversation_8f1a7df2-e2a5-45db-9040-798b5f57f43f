const jwt = require('jsonwebtoken');
const { Admin, AdminRole } = require('../models');
const config = require('../config/config');
const logger = require('../utils/logger');
const { AuthError, PermissionError } = require('./errorHandler');

// 管理员认证中间件
const adminAuth = async (ctx, next) => {
  try {
    // 获取token
    const token = ctx.headers.authorization?.replace('Bearer ', '');
    
    if (!token) {
      throw new AuthError('缺少访问令牌');
    }

    // 验证token
    const decoded = jwt.verify(token, config.jwt.secret);
    
    if (!decoded.adminId) {
      throw new AuthError('无效的管理员令牌');
    }

    // 获取管理员信息
    const admin = await Admin.findByPk(decoded.adminId, {
      include: [
        {
          model: AdminRole,
          as: 'role',
          attributes: ['id', 'name', 'permissions']
        }
      ]
    });

    if (!admin) {
      throw new AuthError('管理员不存在');
    }

    if (admin.status !== 1) {
      throw new AuthError('管理员账号已被禁用');
    }

    if (!admin.role || admin.role.status !== 1) {
      throw new AuthError('管理员角色无效');
    }

    // 解析权限
    let permissions = [];
    try {
      permissions = JSON.parse(admin.role.permissions);
    } catch (error) {
      logger.error('解析管理员权限失败', error);
      permissions = [];
    }

    // 将管理员信息添加到上下文
    ctx.state.admin = admin;
    ctx.state.permissions = permissions;

    await next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      throw new AuthError('无效的访问令牌');
    } else if (error.name === 'TokenExpiredError') {
      throw new AuthError('访问令牌已过期');
    } else {
      throw error;
    }
  }
};

// 权限检查中间件
const checkPermission = (requiredPermission) => {
  return async (ctx, next) => {
    const { permissions } = ctx.state;

    if (!permissions) {
      throw new PermissionError('权限信息不存在');
    }

    // 超级管理员拥有所有权限
    if (permissions.includes('*')) {
      await next();
      return;
    }

    // 检查具体权限
    if (!permissions.includes(requiredPermission)) {
      logger.security('权限不足', {
        adminId: ctx.state.admin?.id,
        requiredPermission,
        userPermissions: permissions,
        path: ctx.path,
        method: ctx.method,
        ip: ctx.ip
      });

      throw new PermissionError(`权限不足，需要权限: ${requiredPermission}`);
    }

    await next();
  };
};

// 记录管理员操作日志
const logAdminAction = (action, target = null) => {
  return async (ctx, next) => {
    const startTime = Date.now();
    let error = null;

    try {
      await next();
    } catch (err) {
      error = err;
      throw err;
    } finally {
      // 记录操作日志
      const { AdminLog } = require('../models');
      const admin = ctx.state.admin;
      
      if (admin) {
        try {
          await AdminLog.create({
            adminId: admin.id,
            action,
            target,
            targetId: ctx.params.id || null,
            description: error ? `操作失败: ${error.message}` : '操作成功',
            ip: ctx.ip,
            userAgent: ctx.headers['user-agent']
          });

          logger.business('管理员操作', admin.id, {
            action,
            target,
            targetId: ctx.params.id,
            success: !error,
            duration: Date.now() - startTime,
            ip: ctx.ip
          });
        } catch (logError) {
          logger.error('记录管理员操作日志失败', logError);
        }
      }
    }
  };
};

module.exports = {
  adminAuth,
  checkPermission,
  logAdminAction
};
