const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Comment = sequelize.define('Comment', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
      comment: '评论ID'
    },
    contentId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '内容ID'
    },
    userId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '用户ID'
    },
    parentId: {
      type: DataTypes.BIGINT,
      allowNull: true,
      comment: '父评论ID'
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: false,
      comment: '评论内容'
    },
    likeCount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '点赞数'
    },
    replyCount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '回复数'
    },
    status: {
      type: DataTypes.TINYINT,
      allowNull: false,
      defaultValue: 1,
      comment: '状态：1正常 2隐藏 3删除'
    },
    ip: {
      type: DataTypes.STRING(45),
      allowNull: true,
      comment: '评论IP'
    }
  }, {
    tableName: 'comments',
    comment: '评论表',
    indexes: [
      {
        fields: ['content_id']
      },
      {
        fields: ['user_id']
      },
      {
        fields: ['parent_id']
      },
      {
        fields: ['status']
      },
      {
        fields: ['created_at']
      }
    ]
  });

  return Comment;
};
