const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Card = sequelize.define('Card', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
      comment: '卡密ID'
    },
    code: {
      type: DataTypes.STRING(32),
      unique: true,
      allowNull: false,
      comment: '卡密码'
    },
    type: {
      type: DataTypes.ENUM('points', 'vip', 'content'),
      allowNull: false,
      comment: '卡密类型：points积分卡 vip会员卡 content内容卡'
    },
    value: {
      type: DataTypes.TEXT,
      allowNull: false,
      comment: '卡密价值（JSON格式）'
    },
    batchId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '批次ID'
    },
    status: {
      type: DataTypes.TINYINT,
      allowNull: false,
      defaultValue: 1,
      comment: '状态：1未使用 2已使用 3已过期 4已作废'
    },
    usedBy: {
      type: DataTypes.BIGINT,
      allowNull: true,
      comment: '使用者ID'
    },
    usedAt: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '使用时间'
    },
    expireAt: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '过期时间'
    }
  }, {
    tableName: 'cards',
    comment: '卡密表',
    indexes: [
      {
        unique: true,
        fields: ['code']
      },
      {
        fields: ['batch_id']
      },
      {
        fields: ['status']
      },
      {
        fields: ['type']
      },
      {
        fields: ['used_by']
      }
    ]
  });

  return Card;
};
