const axios = require('axios');
const crypto = require('crypto');
const QRCode = require('qrcode');
const { User, ShareLog } = require('../models');
const config = require('../config/config');
const logger = require('../utils/logger');
const { BusinessError, ValidationError } = require('../middleware/errorHandler');

class WechatController {
  // 微信登录
  async login(ctx) {
    const { code, userInfo, inviteCode } = ctx.request.body;

    if (!code) {
      throw new ValidationError('微信授权码不能为空');
    }

    try {
      // 获取微信用户信息
      const wechatUser = await this.getWechatUserInfo(code);
      
      // 查找或创建用户
      let user = await User.findByOpenid(wechatUser.openid);
      
      if (!user) {
        // 处理邀请关系
        let inviter = null;
        if (inviteCode) {
          inviter = await User.findByInviteCode(inviteCode);
        }

        // 创建新用户
        user = await User.create({
          openid: wechatUser.openid,
          unionid: wechatUser.unionid,
          nickname: userInfo?.nickName || wechatUser.nickname || '微信用户',
          avatar: userInfo?.avatarUrl || wechatUser.headimgurl,
          gender: userInfo?.gender || wechatUser.sex || 0,
          inviterId: inviter ? inviter.id : null,
          points: config.points.other.completeProfile
        });

        // 记录邀请关系
        if (inviter) {
          const { InviteLog } = require('../models');
          await InviteLog.create({
            inviterId: inviter.id,
            inviteeId: user.id,
            rewardPoints: config.points.invite.register
          });

          // 给邀请人奖励积分
          await inviter.addPoints(
            config.points.invite.register,
            `邀请用户${user.nickname}注册`
          );
        }
      } else {
        // 更新用户信息
        if (userInfo) {
          user.nickname = userInfo.nickName || user.nickname;
          user.avatar = userInfo.avatarUrl || user.avatar;
          user.gender = userInfo.gender || user.gender;
          await user.save();
        }
      }

      // 更新登录信息
      await user.updateLoginInfo(ctx.ip);

      // 生成JWT令牌
      const authController = require('./authController');
      const token = authController.generateToken(user);

      logger.business('微信登录', user.id, { 
        openid: wechatUser.openid,
        ip: ctx.ip 
      });

      ctx.body = {
        code: 200,
        message: '登录成功',
        data: {
          user: user.toJSON(),
          token
        }
      };
    } catch (error) {
      logger.error('微信登录失败', error);
      throw new BusinessError('微信登录失败，请重试');
    }
  }

  // 获取微信用户信息
  async getUserInfo(ctx) {
    const { code } = ctx.query;

    if (!code) {
      throw new ValidationError('授权码不能为空');
    }

    try {
      const userInfo = await this.getWechatUserInfo(code);
      
      ctx.body = {
        code: 200,
        message: '获取成功',
        data: userInfo
      };
    } catch (error) {
      logger.error('获取微信用户信息失败', error);
      throw new BusinessError('获取用户信息失败');
    }
  }

  // 创建微信支付订单
  async createPayment(ctx) {
    const { amount, description, orderNo } = ctx.request.body;
    const userId = ctx.state.user.id;

    if (!amount || !description || !orderNo) {
      throw new ValidationError('支付参数不完整');
    }

    try {
      // TODO: 调用微信支付API创建订单
      // 这里简化处理，实际需要调用微信支付接口
      
      const paymentData = {
        orderNo,
        amount,
        description,
        userId,
        prepayId: `prepay_${Date.now()}`,
        paySign: this.generatePaySign(orderNo, amount)
      };

      logger.business('创建支付订单', userId, { orderNo, amount });

      ctx.body = {
        code: 200,
        message: '创建成功',
        data: paymentData
      };
    } catch (error) {
      logger.error('创建支付订单失败', error);
      throw new BusinessError('创建支付订单失败');
    }
  }

  // 微信支付回调
  async paymentNotify(ctx) {
    const { body } = ctx.request;

    try {
      // TODO: 验证微信支付回调签名
      // 处理支付结果
      
      logger.info('收到微信支付回调', body);

      // 返回成功响应给微信
      ctx.body = '<xml><return_code><![CDATA[SUCCESS]]></return_code></xml>';
    } catch (error) {
      logger.error('处理支付回调失败', error);
      ctx.body = '<xml><return_code><![CDATA[FAIL]]></return_code></xml>';
    }
  }

  // 生成分享二维码
  async generateQRCode(ctx) {
    const { type, contentId, inviteCode } = ctx.request.body;
    const userId = ctx.state.user?.id;

    let qrData = '';
    
    if (type === 'invite' && inviteCode) {
      // 邀请二维码
      qrData = `${ctx.origin}/invite/${inviteCode}`;
    } else if (type === 'content' && contentId) {
      // 内容分享二维码
      const shareParam = userId ? `?from=${userId}` : '';
      qrData = `${ctx.origin}/content/${contentId}${shareParam}`;
    } else {
      throw new ValidationError('二维码参数不正确');
    }

    try {
      const qrCodeDataURL = await QRCode.toDataURL(qrData, {
        width: 200,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      });

      ctx.body = {
        code: 200,
        message: '生成成功',
        data: {
          qrCode: qrCodeDataURL,
          url: qrData
        }
      };
    } catch (error) {
      logger.error('生成二维码失败', error);
      throw new BusinessError('生成二维码失败');
    }
  }

  // 微信分享回调
  async shareCallback(ctx) {
    const { contentId, from } = ctx.query;

    if (contentId && from) {
      try {
        // 记录分享点击
        const shareLog = await ShareLog.findOne({
          where: {
            userId: from,
            contentId,
            shareType: 'wechat'
          },
          order: [['createdAt', 'DESC']]
        });

        if (shareLog) {
          await shareLog.increment('clickCount');
          
          // TODO: 根据配置给分享者奖励积分
          
          logger.business('分享点击', from, { contentId });
        }
      } catch (error) {
        logger.error('处理分享回调失败', error);
      }
    }

    // 重定向到内容页面
    ctx.redirect(`/content/${contentId}`);
  }

  // 获取微信用户信息（内部方法）
  async getWechatUserInfo(code) {
    const { appId, appSecret } = config.wechat;
    
    // 第一步：获取access_token
    const tokenUrl = `https://api.weixin.qq.com/sns/oauth2/access_token?appid=${appId}&secret=${appSecret}&code=${code}&grant_type=authorization_code`;
    
    const tokenResponse = await axios.get(tokenUrl);
    const tokenData = tokenResponse.data;
    
    if (tokenData.errcode) {
      throw new Error(`获取access_token失败: ${tokenData.errmsg}`);
    }

    // 第二步：获取用户信息
    const userUrl = `https://api.weixin.qq.com/sns/userinfo?access_token=${tokenData.access_token}&openid=${tokenData.openid}&lang=zh_CN`;
    
    const userResponse = await axios.get(userUrl);
    const userData = userResponse.data;
    
    if (userData.errcode) {
      throw new Error(`获取用户信息失败: ${userData.errmsg}`);
    }

    return userData;
  }

  // 生成支付签名（内部方法）
  generatePaySign(orderNo, amount) {
    const timestamp = Math.floor(Date.now() / 1000);
    const nonceStr = Math.random().toString(36).substr(2, 15);
    
    // 简化的签名生成，实际应该按照微信支付规范
    const signStr = `orderNo=${orderNo}&amount=${amount}&timestamp=${timestamp}&nonceStr=${nonceStr}`;
    
    return crypto.createHash('md5').update(signStr).digest('hex');
  }
}

module.exports = new WechatController();
