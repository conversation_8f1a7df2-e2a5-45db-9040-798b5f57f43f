# 📘 微信知识付费平台

一个覆盖考试与自学用户、支持微信与网页访问、具有积分激励与传播裂变能力的内容分发平台。

## 🎯 项目特色

- **多端统一**：微信小程序 + 网页端无缝切换
- **激励体系**：积分、VIP、邀请多重激励机制  
- **内容丰富**：文章 + 网盘资源多样化内容形式
- **社交裂变**：基于微信生态的天然传播优势

## 🏗️ 技术架构

### 前端
- **微信小程序**：原生开发，遵循微信设计规范
- **网页端**：Vue3 + TypeScript + Vite
- **管理后台**：Vue3 + Naive UI

### 后端
- **API服务**：Node.js + Koa + TypeScript
- **数据库**：MySQL 8.0
- **缓存**：Redis
- **存储**：支持OSS/COS/MinIO

## 📁 项目结构

```
zhis/
├── backend/                 # 后端API服务
│   ├── src/
│   │   ├── controllers/     # 控制器
│   │   ├── models/         # 数据模型
│   │   ├── services/       # 业务逻辑
│   │   ├── middleware/     # 中间件
│   │   ├── routes/         # 路由配置
│   │   ├── utils/          # 工具函数
│   │   └── config/         # 配置文件
│   ├── database/           # 数据库相关
│   │   ├── migrations/     # 数据库迁移
│   │   └── seeds/          # 初始数据
│   └── tests/              # 测试文件
├── miniprogram/            # 微信小程序
│   ├── pages/              # 页面
│   ├── components/         # 组件
│   ├── utils/              # 工具函数
│   └── api/                # API接口
├── web/                    # 网页端
│   ├── src/
│   │   ├── views/          # 页面组件
│   │   ├── components/     # 公共组件
│   │   ├── api/            # API接口
│   │   ├── store/          # 状态管理
│   │   ├── router/         # 路由配置
│   │   └── utils/          # 工具函数
│   └── public/             # 静态资源
├── admin/                  # 管理后台
│   ├── src/
│   │   ├── views/          # 页面组件
│   │   ├── components/     # 公共组件
│   │   ├── api/            # API接口
│   │   └── store/          # 状态管理
│   └── public/             # 静态资源
├── docs/                   # 项目文档
├── docker/                 # Docker配置
└── scripts/                # 脚本文件
```

## 🚀 快速开始

### 环境要求
- Node.js >= 16.0.0
- MySQL >= 8.0
- Redis >= 6.0
- 微信开发者工具

### 安装依赖
```bash
# 安装后端依赖
cd backend && npm install

# 安装网页端依赖
cd web && npm install

# 安装管理后台依赖
cd admin && npm install
```

### 配置环境
1. 复制配置文件：`cp backend/src/config/config.example.js backend/src/config/config.js`
2. 修改数据库连接信息
3. 配置微信小程序AppID和Secret

### 启动服务
```bash
# 启动后端服务
cd backend && npm run dev

# 启动网页端
cd web && npm run dev

# 启动管理后台
cd admin && npm run dev
```

## 📖 功能模块

### 核心功能
- [x] 用户系统（微信登录、手机号登录）
- [x] 内容管理（文章、网盘资源）
- [x] 积分系统（获取、消耗、配置）
- [x] 解锁系统（积分、VIP、卡密）
- [x] 评论互动（评论、点赞、收藏）
- [x] 分享邀请（邀请码、分享统计）
- [x] 个性化推荐
- [x] 管理后台

### 高级功能
- [x] 卡密系统
- [x] VIP会员
- [x] 数据统计
- [x] 权限管理（RBAC）
- [x] 积分配置管理

## 🔧 开发指南

### API文档
- 后端API文档：http://localhost:3000/docs
- 接口规范：RESTful API设计

### 数据库
- 数据库设计文档：[docs/database.md](docs/database.md)
- 迁移命令：`npm run migrate`

### 部署
- Docker部署：`docker-compose up -d`
- 生产环境配置：[docs/deployment.md](docs/deployment.md)

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📞 联系我们

如有问题，请联系开发团队。
