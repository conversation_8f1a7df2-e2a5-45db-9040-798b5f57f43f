const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const AdminRole = sequelize.define('AdminRole', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      comment: '角色ID'
    },
    name: {
      type: DataTypes.STRING(50),
      unique: true,
      allowNull: false,
      comment: '角色名称'
    },
    permissions: {
      type: DataTypes.TEXT,
      allowNull: false,
      comment: '权限列表（JSON格式）'
    },
    description: {
      type: DataTypes.STRING(200),
      allowNull: true,
      comment: '角色描述'
    },
    status: {
      type: DataTypes.TINYINT,
      allowNull: false,
      defaultValue: 1,
      comment: '状态：1启用 0禁用'
    }
  }, {
    tableName: 'admin_roles',
    comment: '管理员角色表',
    indexes: [
      {
        unique: true,
        fields: ['name']
      },
      {
        fields: ['status']
      }
    ]
  });

  return AdminRole;
};
