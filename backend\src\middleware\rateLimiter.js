const { RateLimiterRedis, RateLimiterMemory } = require('rate-limiter-flexible');
const Redis = require('redis');
const config = require('../config/config');
const logger = require('../utils/logger');

let rateLimiter;

// 尝试连接Redis，失败则使用内存限流
try {
  const redisClient = Redis.createClient({
    host: config.redis.host,
    port: config.redis.port,
    password: config.redis.password,
    db: config.redis.db
  });

  rateLimiter = new RateLimiterRedis({
    storeClient: redisClient,
    keyPrefix: 'rate_limit',
    points: config.rateLimit.max, // 请求次数
    duration: Math.floor(config.rateLimit.windowMs / 1000), // 时间窗口（秒）
    blockDuration: 60, // 阻塞时间（秒）
    execEvenly: true // 平均分配请求
  });

  logger.info('限流器已连接Redis');
} catch (error) {
  // Redis连接失败，使用内存限流
  rateLimiter = new RateLimiterMemory({
    keyPrefix: 'rate_limit',
    points: config.rateLimit.max,
    duration: Math.floor(config.rateLimit.windowMs / 1000),
    blockDuration: 60,
    execEvenly: true
  });

  logger.warn('Redis连接失败，使用内存限流器', error);
}

// 不同类型的限流器
const limiters = {
  // 通用API限流
  api: rateLimiter,
  
  // 登录限流（更严格）
  login: new RateLimiterMemory({
    keyPrefix: 'login_limit',
    points: 5, // 5次尝试
    duration: 900, // 15分钟
    blockDuration: 900 // 阻塞15分钟
  }),

  // 短信验证码限流
  sms: new RateLimiterMemory({
    keyPrefix: 'sms_limit',
    points: 3, // 3次发送
    duration: 3600, // 1小时
    blockDuration: 3600 // 阻塞1小时
  }),

  // 积分获取限流
  points: new RateLimiterMemory({
    keyPrefix: 'points_limit',
    points: 50, // 50次操作
    duration: 3600, // 1小时
    blockDuration: 300 // 阻塞5分钟
  })
};

// 获取客户端标识
const getClientId = (ctx) => {
  // 优先使用用户ID，其次使用IP
  return ctx.state.user?.id || ctx.ip;
};

// 通用限流中间件
const createRateLimitMiddleware = (limiterType = 'api') => {
  return async (ctx, next) => {
    const limiter = limiters[limiterType];
    const clientId = getClientId(ctx);
    
    try {
      await limiter.consume(clientId);
      await next();
    } catch (rejRes) {
      // 限流触发
      const secs = Math.round(rejRes.msBeforeNext / 1000) || 1;
      
      ctx.set('Retry-After', String(secs));
      ctx.set('X-RateLimit-Limit', limiter.points);
      ctx.set('X-RateLimit-Remaining', rejRes.remainingPoints || 0);
      ctx.set('X-RateLimit-Reset', new Date(Date.now() + rejRes.msBeforeNext));

      // 记录限流日志
      logger.security('Rate limit exceeded', {
        clientId,
        limiterType,
        path: ctx.path,
        method: ctx.method,
        userAgent: ctx.headers['user-agent'],
        retryAfter: secs
      });

      ctx.status = 429;
      ctx.body = {
        code: 429,
        error: 'TOO_MANY_REQUESTS',
        message: config.rateLimit.message,
        retryAfter: secs,
        timestamp: new Date().toISOString()
      };
    }
  };
};

// 默认API限流中间件
const rateLimitMiddleware = createRateLimitMiddleware('api');

// 特定路由限流中间件
const loginRateLimit = createRateLimitMiddleware('login');
const smsRateLimit = createRateLimitMiddleware('sms');
const pointsRateLimit = createRateLimitMiddleware('points');

// 重置限流（管理员功能）
const resetRateLimit = async (clientId, limiterType = 'api') => {
  try {
    const limiter = limiters[limiterType];
    await limiter.delete(clientId);
    return true;
  } catch (error) {
    logger.error('重置限流失败', error);
    return false;
  }
};

// 获取限流状态
const getRateLimitStatus = async (clientId, limiterType = 'api') => {
  try {
    const limiter = limiters[limiterType];
    const res = await limiter.get(clientId);
    
    if (res) {
      return {
        limit: limiter.points,
        remaining: res.remainingPoints,
        reset: new Date(Date.now() + res.msBeforeNext),
        blocked: res.msBeforeNext > 0 && res.remainingPoints === 0
      };
    }
    
    return {
      limit: limiter.points,
      remaining: limiter.points,
      reset: null,
      blocked: false
    };
  } catch (error) {
    logger.error('获取限流状态失败', error);
    return null;
  }
};

module.exports = {
  rateLimitMiddleware,
  createRateLimitMiddleware,
  loginRateLimit,
  smsRateLimit,
  pointsRateLimit,
  resetRateLimit,
  getRateLimitStatus
};
