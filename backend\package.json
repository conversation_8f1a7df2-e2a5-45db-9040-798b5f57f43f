{"name": "zhis-backend", "version": "1.0.0", "description": "微信知识付费平台后端API服务", "main": "src/app.js", "scripts": {"start": "node scripts/start.js", "dev": "nodemon scripts/start.js", "app": "node src/app.js", "init": "node database/init.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "keywords": ["knowledge", "payment", "wechat", "miniprogram", "nodejs", "koa"], "author": "zhis-team", "license": "MIT", "dependencies": {"koa": "^2.14.2", "koa-router": "^12.0.0", "koa-bodyparser": "^4.4.1", "koa-cors": "^0.0.16", "koa-static": "^5.0.0", "koa-jwt": "^4.0.4", "jsonwebtoken": "^9.0.2", "mysql2": "^3.6.5", "sequelize": "^6.35.2", "redis": "^4.6.11", "bcryptjs": "^2.4.3", "joi": "^17.11.0", "axios": "^1.6.2", "multer": "^1.4.5-lts.1", "moment": "^2.29.4", "uuid": "^9.0.1", "qrcode": "^1.5.3", "crypto": "^1.0.1", "dotenv": "^16.3.1", "winston": "^3.11.0", "helmet": "^7.1.0", "rate-limiter-flexible": "^4.0.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}