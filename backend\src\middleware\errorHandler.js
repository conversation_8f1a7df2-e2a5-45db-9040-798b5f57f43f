const logger = require('../utils/logger');

// 自定义错误类
class AppError extends Error {
  constructor(message, statusCode = 500, code = 'INTERNAL_ERROR') {
    super(message);
    this.statusCode = statusCode;
    this.code = code;
    this.isOperational = true;
    
    Error.captureStackTrace(this, this.constructor);
  }
}

// 业务错误类
class BusinessError extends AppError {
  constructor(message, code = 'BUSINESS_ERROR') {
    super(message, 400, code);
  }
}

// 认证错误类
class AuthError extends AppError {
  constructor(message = '认证失败', code = 'AUTH_ERROR') {
    super(message, 401, code);
  }
}

// 权限错误类
class PermissionError extends AppError {
  constructor(message = '权限不足', code = 'PERMISSION_ERROR') {
    super(message, 403, code);
  }
}

// 资源不存在错误类
class NotFoundError extends AppError {
  constructor(message = '资源不存在', code = 'NOT_FOUND') {
    super(message, 404, code);
  }
}

// 验证错误类
class ValidationError extends AppError {
  constructor(message, errors = []) {
    super(message, 422, 'VALIDATION_ERROR');
    this.errors = errors;
  }
}

// 错误处理中间件
const errorHandler = async (ctx, next) => {
  try {
    await next();
  } catch (error) {
    // 记录错误日志
    logger.error('请求处理错误', error, {
      url: ctx.url,
      method: ctx.method,
      ip: ctx.ip,
      userAgent: ctx.headers['user-agent'],
      userId: ctx.state.user?.id
    });

    // 设置默认错误信息
    let statusCode = 500;
    let code = 'INTERNAL_ERROR';
    let message = '服务器内部错误';
    let errors = [];

    // 处理不同类型的错误
    if (error instanceof AppError) {
      // 自定义应用错误
      statusCode = error.statusCode;
      code = error.code;
      message = error.message;
      if (error instanceof ValidationError) {
        errors = error.errors;
      }
    } else if (error.name === 'ValidationError') {
      // Joi验证错误
      statusCode = 422;
      code = 'VALIDATION_ERROR';
      message = '请求参数验证失败';
      errors = error.details?.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      })) || [];
    } else if (error.name === 'SequelizeValidationError') {
      // Sequelize验证错误
      statusCode = 422;
      code = 'VALIDATION_ERROR';
      message = '数据验证失败';
      errors = error.errors?.map(err => ({
        field: err.path,
        message: err.message
      })) || [];
    } else if (error.name === 'SequelizeUniqueConstraintError') {
      // 唯一约束错误
      statusCode = 409;
      code = 'DUPLICATE_ERROR';
      message = '数据已存在';
      errors = error.errors?.map(err => ({
        field: err.path,
        message: `${err.path} 已存在`
      })) || [];
    } else if (error.name === 'JsonWebTokenError') {
      // JWT错误
      statusCode = 401;
      code = 'INVALID_TOKEN';
      message = '无效的访问令牌';
    } else if (error.name === 'TokenExpiredError') {
      // JWT过期错误
      statusCode = 401;
      code = 'TOKEN_EXPIRED';
      message = '访问令牌已过期';
    } else if (error.status === 401) {
      // 认证错误
      statusCode = 401;
      code = 'UNAUTHORIZED';
      message = '未授权访问';
    } else if (error.status === 403) {
      // 权限错误
      statusCode = 403;
      code = 'FORBIDDEN';
      message = '权限不足';
    } else if (error.status === 404) {
      // 404错误
      statusCode = 404;
      code = 'NOT_FOUND';
      message = '请求的资源不存在';
    }

    // 设置响应
    ctx.status = statusCode;
    ctx.body = {
      code: statusCode,
      error: code,
      message,
      ...(errors.length > 0 && { errors }),
      timestamp: new Date().toISOString(),
      path: ctx.path,
      ...(process.env.NODE_ENV === 'development' && { 
        stack: error.stack 
      })
    };

    // 触发应用级错误事件
    ctx.app.emit('error', error, ctx);
  }
};

module.exports = {
  errorHandler,
  AppError,
  BusinessError,
  AuthError,
  PermissionError,
  NotFoundError,
  ValidationError
};
