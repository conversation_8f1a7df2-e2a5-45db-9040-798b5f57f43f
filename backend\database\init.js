const { sequelize, testConnection, syncDatabase } = require('../src/models');
const { PointConfig, AdminRole, Admin, Category, UiConfig } = require('../src/models');
const bcrypt = require('bcryptjs');
const logger = require('../src/utils/logger');

async function initDatabase() {
  try {
    logger.info('开始初始化数据库...');

    // 测试数据库连接
    await testConnection();

    // 同步数据库结构
    await syncDatabase(false); // 设置为true会删除所有数据重建

    // 初始化积分配置
    await initPointConfig();

    // 初始化管理员角色
    await initAdminRoles();

    // 初始化管理员账号
    await initAdminUsers();

    // 初始化分类
    await initCategories();

    // 初始化UI配置
    await initUiConfig();

    logger.info('数据库初始化完成！');
  } catch (error) {
    logger.error('数据库初始化失败', error);
    process.exit(1);
  }
}

// 初始化积分配置
async function initPointConfig() {
  logger.info('初始化积分配置...');
  
  const configs = await PointConfig.initDefaultConfigs();
  logger.info(`初始化了 ${configs.length} 个积分配置`);
}

// 初始化管理员角色
async function initAdminRoles() {
  logger.info('初始化管理员角色...');

  const roles = [
    {
      name: '超级管理员',
      permissions: JSON.stringify(['*']),
      description: '拥有所有权限'
    },
    {
      name: '系统管理员',
      permissions: JSON.stringify([
        'user.view', 'user.edit', 'user.ban',
        'content.view', 'content.edit', 'content.delete',
        'point.config', 'system.config'
      ]),
      description: '系统管理权限'
    },
    {
      name: '内容管理员',
      permissions: JSON.stringify([
        'content.view', 'content.create', 'content.edit',
        'category.manage', 'tag.manage', 'comment.manage'
      ]),
      description: '内容管理权限'
    },
    {
      name: '运营人员',
      permissions: JSON.stringify([
        'user.view', 'content.view', 'stats.view',
        'card.manage', 'activity.manage'
      ]),
      description: '运营管理权限'
    }
  ];

  for (const roleData of roles) {
    const [role, created] = await AdminRole.findOrCreate({
      where: { name: roleData.name },
      defaults: roleData
    });

    if (created) {
      logger.info(`创建角色: ${roleData.name}`);
    }
  }
}

// 初始化管理员账号
async function initAdminUsers() {
  logger.info('初始化管理员账号...');

  // 查找超级管理员角色
  const superAdminRole = await AdminRole.findOne({
    where: { name: '超级管理员' }
  });

  if (!superAdminRole) {
    throw new Error('超级管理员角色不存在');
  }

  // 创建默认超级管理员
  const [admin, created] = await Admin.findOrCreate({
    where: { username: 'admin' },
    defaults: {
      username: 'admin',
      password: 'admin123',
      nickname: '超级管理员',
      email: '<EMAIL>',
      roleId: superAdminRole.id
    }
  });

  if (created) {
    logger.info('创建默认管理员账号: admin/admin123');
  }
}

// 初始化分类
async function initCategories() {
  logger.info('初始化内容分类...');

  const categories = [
    {
      name: '考研资料',
      slug: 'kaoyan',
      description: '考研相关学习资料',
      icon: '📚',
      sortOrder: 100
    },
    {
      name: '公务员考试',
      slug: 'gongwuyuan',
      description: '公务员考试资料',
      icon: '🏛️',
      sortOrder: 90
    },
    {
      name: '职业技能',
      slug: 'zhiye',
      description: '各类职业技能学习',
      icon: '💼',
      sortOrder: 80
    },
    {
      name: '编程开发',
      slug: 'programming',
      description: '编程开发相关资料',
      icon: '💻',
      sortOrder: 70
    },
    {
      name: '设计创意',
      slug: 'design',
      description: '设计创意类资源',
      icon: '🎨',
      sortOrder: 60
    },
    {
      name: '语言学习',
      slug: 'language',
      description: '外语学习资料',
      icon: '🌍',
      sortOrder: 50
    }
  ];

  for (const categoryData of categories) {
    const [category, created] = await Category.findOrCreate({
      where: { slug: categoryData.slug },
      defaults: categoryData
    });

    if (created) {
      logger.info(`创建分类: ${categoryData.name}`);
    }
  }
}

// 初始化UI配置
async function initUiConfig() {
  logger.info('初始化UI配置...');

  const configs = [
    {
      key: 'site_name',
      value: '知识付费平台',
      description: '网站名称'
    },
    {
      key: 'site_description',
      value: '专业的知识付费学习平台',
      description: '网站描述'
    },
    {
      key: 'enable_sign',
      value: 'true',
      description: '是否启用签到功能'
    },
    {
      key: 'enable_ad',
      value: 'true',
      description: '是否启用广告功能'
    },
    {
      key: 'enable_invite',
      value: 'true',
      description: '是否启用邀请功能'
    },
    {
      key: 'enable_share',
      value: 'true',
      description: '是否启用分享功能'
    },
    {
      key: 'home_banner',
      value: JSON.stringify([
        {
          title: '欢迎来到知识付费平台',
          image: '/images/banner1.jpg',
          link: ''
        }
      ]),
      description: '首页轮播图配置'
    },
    {
      key: 'contact_info',
      value: JSON.stringify({
        qq: '123456789',
        wechat: 'zhis_platform',
        email: '<EMAIL>'
      }),
      description: '联系方式'
    }
  ];

  for (const configData of configs) {
    const [config, created] = await UiConfig.findOrCreate({
      where: { key: configData.key },
      defaults: configData
    });

    if (created) {
      logger.info(`创建UI配置: ${configData.key}`);
    }
  }
}

// 如果直接运行此文件，则执行初始化
if (require.main === module) {
  initDatabase().then(() => {
    process.exit(0);
  }).catch((error) => {
    logger.error('初始化失败', error);
    process.exit(1);
  });
}

module.exports = {
  initDatabase,
  initPointConfig,
  initAdminRoles,
  initAdminUsers,
  initCategories,
  initUiConfig
};
