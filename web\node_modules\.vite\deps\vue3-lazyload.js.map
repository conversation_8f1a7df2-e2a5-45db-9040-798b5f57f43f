{"version": 3, "sources": ["../../vue3-lazyload/dist/index.mjs"], "sourcesContent": ["import { ref, onMounted, onUnmounted, watch } from 'vue-demi';\n\nvar LifecycleEnum = /* @__PURE__ */ ((LifecycleEnum2) => {\n  LifecycleEnum2[\"LOADING\"] = \"loading\";\n  LifecycleEnum2[\"LOADED\"] = \"loaded\";\n  LifecycleEnum2[\"ERROR\"] = \"error\";\n  return LifecycleEnum2;\n})(LifecycleEnum || {});\n\nconst inBrowser = typeof window !== \"undefined\" && window !== null;\nconst hasIntersectionObserver = checkIntersectionObserver();\nconst isEnumerable = Object.prototype.propertyIsEnumerable;\nconst getSymbols = Object.getOwnPropertySymbols;\nfunction isObject(val) {\n  return typeof val === \"function\" || toString.call(val) === \"[object Object]\";\n}\nfunction isPrimitive(val) {\n  return typeof val === \"object\" ? val === null : typeof val !== \"function\";\n}\nfunction isValidKey(key) {\n  return key !== \"__proto__\" && key !== \"constructor\" && key !== \"prototype\";\n}\nfunction checkIntersectionObserver() {\n  if (inBrowser && \"IntersectionObserver\" in window && \"IntersectionObserverEntry\" in window && \"intersectionRatio\" in window.IntersectionObserverEntry.prototype) {\n    if (!(\"isIntersecting\" in window.IntersectionObserverEntry.prototype)) {\n      Object.defineProperty(window.IntersectionObserverEntry.prototype, \"isIntersecting\", {\n        get() {\n          return this.intersectionRatio > 0;\n        }\n      });\n    }\n    return true;\n  }\n  return false;\n}\nfunction assignSymbols(target, ...args) {\n  if (!isObject(target))\n    throw new TypeError(\"expected the first argument to be an object\");\n  if (args.length === 0 || typeof Symbol !== \"function\" || typeof getSymbols !== \"function\")\n    return target;\n  for (const arg of args) {\n    const names = getSymbols(arg);\n    for (const key of names) {\n      if (isEnumerable.call(arg, key))\n        target[key] = arg[key];\n    }\n  }\n  return target;\n}\nfunction assign(target, ...args) {\n  let i = 0;\n  if (isPrimitive(target))\n    target = args[i++];\n  if (!target)\n    target = {};\n  for (; i < args.length; i++) {\n    if (isObject(args[i])) {\n      for (const key of Object.keys(args[i])) {\n        if (isValidKey(key)) {\n          if (isObject(target[key]) && isObject(args[i][key]))\n            assign(target[key], args[i][key]);\n          else\n            target[key] = args[i][key];\n        }\n      }\n      assignSymbols(target, args[i]);\n    }\n  }\n  return target;\n}\n\nconst DEFAULT_LOADING = \"data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7\";\nconst DEFAULT_ERROR = \"\";\n\nconst DEFAULT_OBSERVER_OPTIONS = {\n  rootMargin: \"0px\",\n  threshold: 0\n};\nconst TIMEOUT_ID_DATA_ATTR = \"data-lazy-timeout-id\";\nclass Lazy {\n  constructor(options) {\n    this.options = {\n      loading: DEFAULT_LOADING,\n      error: DEFAULT_ERROR,\n      observerOptions: DEFAULT_OBSERVER_OPTIONS,\n      log: true,\n      lifecycle: {},\n      logLevel: \"error\"\n    };\n    this._images = /* @__PURE__ */ new WeakMap();\n    this.config(options);\n  }\n  config(options = {}) {\n    assign(this.options, options);\n  }\n  mount(el, binding) {\n    if (!el)\n      return;\n    const { src, loading, error, lifecycle, delay } = this._valueFormatter(typeof binding === \"string\" ? binding : binding.value);\n    this._lifecycle(LifecycleEnum.LOADING, lifecycle, el);\n    el.setAttribute(\"src\", loading || DEFAULT_LOADING);\n    if (!hasIntersectionObserver) {\n      this.loadImages(el, src, error, lifecycle);\n      this._log(() => {\n        this._logger(\"Not support IntersectionObserver!\");\n      });\n    }\n    this._initIntersectionObserver(el, src, error, lifecycle, delay);\n  }\n  update(el, binding) {\n    if (!el)\n      return;\n    this._realObserver(el)?.unobserve(el);\n    const { src, error, lifecycle, delay } = this._valueFormatter(typeof binding === \"string\" ? binding : binding.value);\n    this._initIntersectionObserver(el, src, error, lifecycle, delay);\n  }\n  unmount(el) {\n    if (!el)\n      return;\n    this._realObserver(el)?.unobserve(el);\n    this._images.delete(el);\n  }\n  loadImages(el, src, error, lifecycle) {\n    this._setImageSrc(el, src, error, lifecycle);\n  }\n  _setImageSrc(el, src, error, lifecycle) {\n    if (el.tagName.toLowerCase() === \"img\") {\n      if (src) {\n        const preSrc = el.getAttribute(\"src\");\n        if (preSrc !== src)\n          el.setAttribute(\"src\", src);\n      }\n      this._listenImageStatus(el, () => {\n        this._lifecycle(LifecycleEnum.LOADED, lifecycle, el);\n      }, () => {\n        el.onload = null;\n        this._lifecycle(LifecycleEnum.ERROR, lifecycle, el);\n        this._realObserver(el)?.disconnect();\n        if (error) {\n          const newImageSrc = el.getAttribute(\"src\");\n          if (newImageSrc !== error)\n            el.setAttribute(\"src\", error);\n        }\n        this._log(() => {\n          this._logger(`Image failed to load!And failed src was: ${src} `);\n        });\n      });\n    } else {\n      el.style.backgroundImage = `url('${src}')`;\n    }\n  }\n  _initIntersectionObserver(el, src, error, lifecycle, delay) {\n    const observerOptions = this.options.observerOptions;\n    this._images.set(el, new IntersectionObserver((entries) => {\n      Array.prototype.forEach.call(entries, (entry) => {\n        if (delay && delay > 0)\n          this._delayedIntersectionCallback(el, entry, delay, src, error, lifecycle);\n        else\n          this._intersectionCallback(el, entry, src, error, lifecycle);\n      });\n    }, observerOptions));\n    this._realObserver(el)?.observe(el);\n  }\n  _intersectionCallback(el, entry, src, error, lifecycle) {\n    if (entry.isIntersecting) {\n      this._realObserver(el)?.unobserve(entry.target);\n      this._setImageSrc(el, src, error, lifecycle);\n    }\n  }\n  _delayedIntersectionCallback(el, entry, delay, src, error, lifecycle) {\n    if (entry.isIntersecting) {\n      if (entry.target.hasAttribute(TIMEOUT_ID_DATA_ATTR))\n        return;\n      const timeoutId = setTimeout(() => {\n        this._intersectionCallback(el, entry, src, error, lifecycle);\n        entry.target.removeAttribute(TIMEOUT_ID_DATA_ATTR);\n      }, delay);\n      entry.target.setAttribute(TIMEOUT_ID_DATA_ATTR, String(timeoutId));\n    } else {\n      if (entry.target.hasAttribute(TIMEOUT_ID_DATA_ATTR)) {\n        clearTimeout(Number(entry.target.getAttribute(TIMEOUT_ID_DATA_ATTR)));\n        entry.target.removeAttribute(TIMEOUT_ID_DATA_ATTR);\n      }\n    }\n  }\n  _listenImageStatus(image, success, error) {\n    image.onload = success;\n    image.onerror = error;\n  }\n  _valueFormatter(value) {\n    let src = value;\n    let loading = this.options.loading;\n    let error = this.options.error;\n    let lifecycle = this.options.lifecycle;\n    let delay = this.options.delay;\n    if (isObject(value)) {\n      src = value.src;\n      loading = value.loading || this.options.loading;\n      error = value.error || this.options.error;\n      lifecycle = value.lifecycle || this.options.lifecycle;\n      delay = value.delay || this.options.delay;\n    }\n    return {\n      src,\n      loading,\n      error,\n      lifecycle,\n      delay\n    };\n  }\n  _log(callback) {\n    if (this.options.log)\n      callback();\n  }\n  _lifecycle(life, lifecycle, el) {\n    switch (life) {\n      case LifecycleEnum.LOADING:\n        el?.setAttribute(\"lazy\", LifecycleEnum.LOADING);\n        if (lifecycle?.loading)\n          lifecycle.loading(el);\n        break;\n      case LifecycleEnum.LOADED:\n        el?.setAttribute(\"lazy\", LifecycleEnum.LOADED);\n        if (lifecycle?.loaded)\n          lifecycle.loaded(el);\n        break;\n      case LifecycleEnum.ERROR:\n        el?.setAttribute(\"lazy\", LifecycleEnum.ERROR);\n        if (lifecycle?.error)\n          lifecycle.error(el);\n        break;\n    }\n  }\n  _realObserver(el) {\n    return this._images.get(el);\n  }\n  _logger(message, ...optionalParams) {\n    let log = console.error;\n    switch (this.options.logLevel) {\n      case \"error\":\n        log = console.error;\n        break;\n      case \"warn\":\n        log = console.warn;\n        break;\n      case \"info\":\n        log = console.info;\n        break;\n      case \"debug\":\n        log = console.debug;\n        break;\n    }\n    log(message, optionalParams);\n  }\n}\n\nfunction useLazyload(src, options) {\n  const lazyRef = ref(null);\n  const lazy = new Lazy(options);\n  onMounted(() => {\n    lazyRef.value && lazy.mount(lazyRef.value, src.value);\n  });\n  onUnmounted(() => {\n    lazyRef.value && lazy.unmount(lazyRef.value);\n  });\n  watch(src, (newVal) => {\n    if (src.value)\n      lazy.update(lazyRef.value, newVal);\n  });\n  return lazyRef;\n}\n\nconst index = {\n  install(Vue, options) {\n    const lazy = new Lazy(options);\n    Vue.config.globalProperties.$Lazyload = lazy;\n    Vue.provide(\"Lazyload\", lazy);\n    Vue.directive(\"lazy\", {\n      mounted: lazy.mount.bind(lazy),\n      updated: lazy.update.bind(lazy),\n      unmounted: lazy.unmount.bind(lazy)\n    });\n  }\n};\n\nexport { index as default, useLazyload };\n"], "mappings": ";;;;;;;;;AAEA,IAAI,iBAAiC,CAAC,mBAAmB;AACvD,iBAAe,SAAS,IAAI;AAC5B,iBAAe,QAAQ,IAAI;AAC3B,iBAAe,OAAO,IAAI;AAC1B,SAAO;AACT,GAAG,iBAAiB,CAAC,CAAC;AAEtB,IAAM,YAAY,OAAO,WAAW,eAAe,WAAW;AAC9D,IAAM,0BAA0B,0BAA0B;AAC1D,IAAM,eAAe,OAAO,UAAU;AACtC,IAAM,aAAa,OAAO;AAC1B,SAAS,SAAS,KAAK;AACrB,SAAO,OAAO,QAAQ,cAAc,SAAS,KAAK,GAAG,MAAM;AAC7D;AACA,SAAS,YAAY,KAAK;AACxB,SAAO,OAAO,QAAQ,WAAW,QAAQ,OAAO,OAAO,QAAQ;AACjE;AACA,SAAS,WAAW,KAAK;AACvB,SAAO,QAAQ,eAAe,QAAQ,iBAAiB,QAAQ;AACjE;AACA,SAAS,4BAA4B;AACnC,MAAI,aAAa,0BAA0B,UAAU,+BAA+B,UAAU,uBAAuB,OAAO,0BAA0B,WAAW;AAC/J,QAAI,EAAE,oBAAoB,OAAO,0BAA0B,YAAY;AACrE,aAAO,eAAe,OAAO,0BAA0B,WAAW,kBAAkB;AAAA,QAClF,MAAM;AACJ,iBAAO,KAAK,oBAAoB;AAAA,QAClC;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,cAAc,WAAW,MAAM;AACtC,MAAI,CAAC,SAAS,MAAM;AAClB,UAAM,IAAI,UAAU,6CAA6C;AACnE,MAAI,KAAK,WAAW,KAAK,OAAO,WAAW,cAAc,OAAO,eAAe;AAC7E,WAAO;AACT,aAAW,OAAO,MAAM;AACtB,UAAM,QAAQ,WAAW,GAAG;AAC5B,eAAW,OAAO,OAAO;AACvB,UAAI,aAAa,KAAK,KAAK,GAAG;AAC5B,eAAO,GAAG,IAAI,IAAI,GAAG;AAAA,IACzB;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,OAAO,WAAW,MAAM;AAC/B,MAAI,IAAI;AACR,MAAI,YAAY,MAAM;AACpB,aAAS,KAAK,GAAG;AACnB,MAAI,CAAC;AACH,aAAS,CAAC;AACZ,SAAO,IAAI,KAAK,QAAQ,KAAK;AAC3B,QAAI,SAAS,KAAK,CAAC,CAAC,GAAG;AACrB,iBAAW,OAAO,OAAO,KAAK,KAAK,CAAC,CAAC,GAAG;AACtC,YAAI,WAAW,GAAG,GAAG;AACnB,cAAI,SAAS,OAAO,GAAG,CAAC,KAAK,SAAS,KAAK,CAAC,EAAE,GAAG,CAAC;AAChD,mBAAO,OAAO,GAAG,GAAG,KAAK,CAAC,EAAE,GAAG,CAAC;AAAA;AAEhC,mBAAO,GAAG,IAAI,KAAK,CAAC,EAAE,GAAG;AAAA,QAC7B;AAAA,MACF;AACA,oBAAc,QAAQ,KAAK,CAAC,CAAC;AAAA,IAC/B;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAM,kBAAkB;AACxB,IAAM,gBAAgB;AAEtB,IAAM,2BAA2B;AAAA,EAC/B,YAAY;AAAA,EACZ,WAAW;AACb;AACA,IAAM,uBAAuB;AAC7B,IAAM,OAAN,MAAW;AAAA,EACT,YAAY,SAAS;AACnB,SAAK,UAAU;AAAA,MACb,SAAS;AAAA,MACT,OAAO;AAAA,MACP,iBAAiB;AAAA,MACjB,KAAK;AAAA,MACL,WAAW,CAAC;AAAA,MACZ,UAAU;AAAA,IACZ;AACA,SAAK,UAA0B,oBAAI,QAAQ;AAC3C,SAAK,OAAO,OAAO;AAAA,EACrB;AAAA,EACA,OAAO,UAAU,CAAC,GAAG;AACnB,WAAO,KAAK,SAAS,OAAO;AAAA,EAC9B;AAAA,EACA,MAAM,IAAI,SAAS;AACjB,QAAI,CAAC;AACH;AACF,UAAM,EAAE,KAAK,SAAS,OAAO,WAAW,MAAM,IAAI,KAAK,gBAAgB,OAAO,YAAY,WAAW,UAAU,QAAQ,KAAK;AAC5H,SAAK,WAAW,cAAc,SAAS,WAAW,EAAE;AACpD,OAAG,aAAa,OAAO,WAAW,eAAe;AACjD,QAAI,CAAC,yBAAyB;AAC5B,WAAK,WAAW,IAAI,KAAK,OAAO,SAAS;AACzC,WAAK,KAAK,MAAM;AACd,aAAK,QAAQ,mCAAmC;AAAA,MAClD,CAAC;AAAA,IACH;AACA,SAAK,0BAA0B,IAAI,KAAK,OAAO,WAAW,KAAK;AAAA,EACjE;AAAA,EACA,OAAO,IAAI,SAAS;AA7GtB;AA8GI,QAAI,CAAC;AACH;AACF,eAAK,cAAc,EAAE,MAArB,mBAAwB,UAAU;AAClC,UAAM,EAAE,KAAK,OAAO,WAAW,MAAM,IAAI,KAAK,gBAAgB,OAAO,YAAY,WAAW,UAAU,QAAQ,KAAK;AACnH,SAAK,0BAA0B,IAAI,KAAK,OAAO,WAAW,KAAK;AAAA,EACjE;AAAA,EACA,QAAQ,IAAI;AApHd;AAqHI,QAAI,CAAC;AACH;AACF,eAAK,cAAc,EAAE,MAArB,mBAAwB,UAAU;AAClC,SAAK,QAAQ,OAAO,EAAE;AAAA,EACxB;AAAA,EACA,WAAW,IAAI,KAAK,OAAO,WAAW;AACpC,SAAK,aAAa,IAAI,KAAK,OAAO,SAAS;AAAA,EAC7C;AAAA,EACA,aAAa,IAAI,KAAK,OAAO,WAAW;AACtC,QAAI,GAAG,QAAQ,YAAY,MAAM,OAAO;AACtC,UAAI,KAAK;AACP,cAAM,SAAS,GAAG,aAAa,KAAK;AACpC,YAAI,WAAW;AACb,aAAG,aAAa,OAAO,GAAG;AAAA,MAC9B;AACA,WAAK,mBAAmB,IAAI,MAAM;AAChC,aAAK,WAAW,cAAc,QAAQ,WAAW,EAAE;AAAA,MACrD,GAAG,MAAM;AAtIf;AAuIQ,WAAG,SAAS;AACZ,aAAK,WAAW,cAAc,OAAO,WAAW,EAAE;AAClD,mBAAK,cAAc,EAAE,MAArB,mBAAwB;AACxB,YAAI,OAAO;AACT,gBAAM,cAAc,GAAG,aAAa,KAAK;AACzC,cAAI,gBAAgB;AAClB,eAAG,aAAa,OAAO,KAAK;AAAA,QAChC;AACA,aAAK,KAAK,MAAM;AACd,eAAK,QAAQ,4CAA4C,GAAG,GAAG;AAAA,QACjE,CAAC;AAAA,MACH,CAAC;AAAA,IACH,OAAO;AACL,SAAG,MAAM,kBAAkB,QAAQ,GAAG;AAAA,IACxC;AAAA,EACF;AAAA,EACA,0BAA0B,IAAI,KAAK,OAAO,WAAW,OAAO;AAvJ9D;AAwJI,UAAM,kBAAkB,KAAK,QAAQ;AACrC,SAAK,QAAQ,IAAI,IAAI,IAAI,qBAAqB,CAAC,YAAY;AACzD,YAAM,UAAU,QAAQ,KAAK,SAAS,CAAC,UAAU;AAC/C,YAAI,SAAS,QAAQ;AACnB,eAAK,6BAA6B,IAAI,OAAO,OAAO,KAAK,OAAO,SAAS;AAAA;AAEzE,eAAK,sBAAsB,IAAI,OAAO,KAAK,OAAO,SAAS;AAAA,MAC/D,CAAC;AAAA,IACH,GAAG,eAAe,CAAC;AACnB,eAAK,cAAc,EAAE,MAArB,mBAAwB,QAAQ;AAAA,EAClC;AAAA,EACA,sBAAsB,IAAI,OAAO,KAAK,OAAO,WAAW;AAnK1D;AAoKI,QAAI,MAAM,gBAAgB;AACxB,iBAAK,cAAc,EAAE,MAArB,mBAAwB,UAAU,MAAM;AACxC,WAAK,aAAa,IAAI,KAAK,OAAO,SAAS;AAAA,IAC7C;AAAA,EACF;AAAA,EACA,6BAA6B,IAAI,OAAO,OAAO,KAAK,OAAO,WAAW;AACpE,QAAI,MAAM,gBAAgB;AACxB,UAAI,MAAM,OAAO,aAAa,oBAAoB;AAChD;AACF,YAAM,YAAY,WAAW,MAAM;AACjC,aAAK,sBAAsB,IAAI,OAAO,KAAK,OAAO,SAAS;AAC3D,cAAM,OAAO,gBAAgB,oBAAoB;AAAA,MACnD,GAAG,KAAK;AACR,YAAM,OAAO,aAAa,sBAAsB,OAAO,SAAS,CAAC;AAAA,IACnE,OAAO;AACL,UAAI,MAAM,OAAO,aAAa,oBAAoB,GAAG;AACnD,qBAAa,OAAO,MAAM,OAAO,aAAa,oBAAoB,CAAC,CAAC;AACpE,cAAM,OAAO,gBAAgB,oBAAoB;AAAA,MACnD;AAAA,IACF;AAAA,EACF;AAAA,EACA,mBAAmB,OAAO,SAAS,OAAO;AACxC,UAAM,SAAS;AACf,UAAM,UAAU;AAAA,EAClB;AAAA,EACA,gBAAgB,OAAO;AACrB,QAAI,MAAM;AACV,QAAI,UAAU,KAAK,QAAQ;AAC3B,QAAI,QAAQ,KAAK,QAAQ;AACzB,QAAI,YAAY,KAAK,QAAQ;AAC7B,QAAI,QAAQ,KAAK,QAAQ;AACzB,QAAI,SAAS,KAAK,GAAG;AACnB,YAAM,MAAM;AACZ,gBAAU,MAAM,WAAW,KAAK,QAAQ;AACxC,cAAQ,MAAM,SAAS,KAAK,QAAQ;AACpC,kBAAY,MAAM,aAAa,KAAK,QAAQ;AAC5C,cAAQ,MAAM,SAAS,KAAK,QAAQ;AAAA,IACtC;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,KAAK,UAAU;AACb,QAAI,KAAK,QAAQ;AACf,eAAS;AAAA,EACb;AAAA,EACA,WAAW,MAAM,WAAW,IAAI;AAC9B,YAAQ,MAAM;AAAA,MACZ,KAAK,cAAc;AACjB,iCAAI,aAAa,QAAQ,cAAc;AACvC,YAAI,uCAAW;AACb,oBAAU,QAAQ,EAAE;AACtB;AAAA,MACF,KAAK,cAAc;AACjB,iCAAI,aAAa,QAAQ,cAAc;AACvC,YAAI,uCAAW;AACb,oBAAU,OAAO,EAAE;AACrB;AAAA,MACF,KAAK,cAAc;AACjB,iCAAI,aAAa,QAAQ,cAAc;AACvC,YAAI,uCAAW;AACb,oBAAU,MAAM,EAAE;AACpB;AAAA,IACJ;AAAA,EACF;AAAA,EACA,cAAc,IAAI;AAChB,WAAO,KAAK,QAAQ,IAAI,EAAE;AAAA,EAC5B;AAAA,EACA,QAAQ,YAAY,gBAAgB;AAClC,QAAI,MAAM,QAAQ;AAClB,YAAQ,KAAK,QAAQ,UAAU;AAAA,MAC7B,KAAK;AACH,cAAM,QAAQ;AACd;AAAA,MACF,KAAK;AACH,cAAM,QAAQ;AACd;AAAA,MACF,KAAK;AACH,cAAM,QAAQ;AACd;AAAA,MACF,KAAK;AACH,cAAM,QAAQ;AACd;AAAA,IACJ;AACA,QAAI,SAAS,cAAc;AAAA,EAC7B;AACF;AAEA,SAAS,YAAY,KAAK,SAAS;AACjC,QAAM,UAAU,IAAI,IAAI;AACxB,QAAM,OAAO,IAAI,KAAK,OAAO;AAC7B,YAAU,MAAM;AACd,YAAQ,SAAS,KAAK,MAAM,QAAQ,OAAO,IAAI,KAAK;AAAA,EACtD,CAAC;AACD,cAAY,MAAM;AAChB,YAAQ,SAAS,KAAK,QAAQ,QAAQ,KAAK;AAAA,EAC7C,CAAC;AACD,QAAM,KAAK,CAAC,WAAW;AACrB,QAAI,IAAI;AACN,WAAK,OAAO,QAAQ,OAAO,MAAM;AAAA,EACrC,CAAC;AACD,SAAO;AACT;AAEA,IAAM,QAAQ;AAAA,EACZ,QAAQ,KAAK,SAAS;AACpB,UAAM,OAAO,IAAI,KAAK,OAAO;AAC7B,QAAI,OAAO,iBAAiB,YAAY;AACxC,QAAI,QAAQ,YAAY,IAAI;AAC5B,QAAI,UAAU,QAAQ;AAAA,MACpB,SAAS,KAAK,MAAM,KAAK,IAAI;AAAA,MAC7B,SAAS,KAAK,OAAO,KAAK,IAAI;AAAA,MAC9B,WAAW,KAAK,QAAQ,KAAK,IAAI;AAAA,IACnC,CAAC;AAAA,EACH;AACF;", "names": []}