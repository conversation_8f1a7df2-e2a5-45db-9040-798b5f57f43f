/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    ContentCard: typeof import('./src/components/ContentCard.vue')['default']
    ContentListItem: typeof import('./src/components/ContentListItem.vue')['default']
    ElBacktop: typeof import('element-plus/es')['ElBacktop']
    ElLoadingService: typeof import('element-plus/es')['ElLoadingService']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
}
