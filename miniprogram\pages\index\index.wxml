<!--首页-->
<view class="container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading">
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 主要内容 -->
  <view wx:else>
    <!-- 搜索栏 -->
    <view class="search-bar" bindtap="onSearchTap">
      <view class="search-input">
        <text class="search-icon">🔍</text>
        <text class="search-placeholder">搜索感兴趣的内容</text>
      </view>
    </view>

    <!-- 轮播图 -->
    <view class="banner-section" wx:if="{{banners.length > 0}}">
      <swiper 
        class="banner-swiper" 
        indicator-dots="{{banners.length > 1}}"
        autoplay="{{banners.length > 1}}"
        interval="5000"
        duration="500"
        bindchange="onBannerChange"
      >
        <swiper-item 
          wx:for="{{banners}}" 
          wx:key="index"
          bindtap="onBannerTap"
          data-index="{{index}}"
        >
          <image class="banner-image" src="{{item.image}}" mode="aspectFill" />
          <view class="banner-title">{{item.title}}</view>
        </swiper-item>
      </swiper>
    </view>

    <!-- 快捷功能 -->
    <view class="quick-actions">
      <view class="action-item" bindtap="onSignTap">
        <view class="action-icon">📅</view>
        <view class="action-text">每日签到</view>
      </view>
      <view class="action-item" bindtap="onSearchTap">
        <view class="action-icon">🔍</view>
        <view class="action-text">搜索内容</view>
      </view>
      <view class="action-item" data-category="{{categories[0]}}" bindtap="onCategoryTap">
        <view class="action-icon">📚</view>
        <view class="action-text">热门分类</view>
      </view>
      <view class="action-item" bindtap="onViewMoreRecommend">
        <view class="action-icon">⭐</view>
        <view class="action-text">推荐内容</view>
      </view>
    </view>

    <!-- 统计信息 -->
    <view class="stats-section" wx:if="{{stats}}">
      <view class="stats-item">
        <view class="stats-number">{{stats.totalContents || 0}}</view>
        <view class="stats-label">总内容数</view>
      </view>
      <view class="stats-item">
        <view class="stats-number">{{stats.totalUsers || 0}}</view>
        <view class="stats-label">用户数</view>
      </view>
      <view class="stats-item">
        <view class="stats-number">{{stats.totalUnlocks || 0}}</view>
        <view class="stats-label">解锁次数</view>
      </view>
      <view class="stats-item">
        <view class="stats-number">{{stats.todayContents || 0}}</view>
        <view class="stats-label">今日新增</view>
      </view>
    </view>

    <!-- 分类导航 -->
    <view class="category-section" wx:if="{{categories.length > 0}}">
      <view class="section-header">
        <view class="section-title">热门分类</view>
      </view>
      <scroll-view class="category-scroll" scroll-x="true">
        <view class="category-list">
          <view 
            class="category-item" 
            wx:for="{{categories}}" 
            wx:key="id"
            data-category="{{item}}"
            bindtap="onCategoryTap"
          >
            <view class="category-icon">{{item.icon}}</view>
            <view class="category-name">{{item.name}}</view>
            <view class="category-count">{{item.contentCount}}个内容</view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 热门内容 -->
    <view class="content-section" wx:if="{{hotContents.length > 0}}">
      <view class="section-header">
        <view class="section-title">🔥 热门内容</view>
        <view class="section-more" bindtap="onViewMoreHot">更多</view>
      </view>
      <view class="content-list">
        <view 
          class="content-item" 
          wx:for="{{hotContents}}" 
          wx:key="id"
          data-content="{{item}}"
          bindtap="onContentTap"
        >
          <image class="content-cover" src="{{item.coverImage}}" mode="aspectFill" />
          <view class="content-info">
            <view class="content-title">{{item.title}}</view>
            <view class="content-meta">
              <text class="meta-item">👀 {{item.viewCount}}</text>
              <text class="meta-item">👍 {{item.likeCount}}</text>
              <text class="meta-item" wx:if="{{item.unlockType === 1}}">💰 {{item.unlockPrice}}积分</text>
              <text class="meta-item" wx:if="{{item.unlockType === 2}}">👑 VIP</text>
              <text class="meta-item" wx:if="{{item.unlockType === 4}}">🆓 免费</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 推荐内容 -->
    <view class="content-section" wx:if="{{recommendContents.length > 0}}">
      <view class="section-header">
        <view class="section-title">⭐ 精选推荐</view>
        <view class="section-more" bindtap="onViewMoreRecommend">更多</view>
      </view>
      <view class="content-grid">
        <view 
          class="content-card" 
          wx:for="{{recommendContents}}" 
          wx:key="id"
          data-content="{{item}}"
          bindtap="onContentTap"
        >
          <image class="card-cover" src="{{item.coverImage}}" mode="aspectFill" />
          <view class="card-info">
            <view class="card-title">{{item.title}}</view>
            <view class="card-meta">
              <text wx:if="{{item.unlockType === 1}}">{{item.unlockPrice}}积分</text>
              <text wx:if="{{item.unlockType === 2}}">VIP专享</text>
              <text wx:if="{{item.unlockType === 4}}">免费</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 最新内容 -->
    <view class="content-section" wx:if="{{latestContents.length > 0}}">
      <view class="section-header">
        <view class="section-title">🆕 最新发布</view>
        <view class="section-more" bindtap="onViewMoreLatest">更多</view>
      </view>
      <view class="content-list">
        <view 
          class="content-item" 
          wx:for="{{latestContents}}" 
          wx:key="id"
          data-content="{{item}}"
          bindtap="onContentTap"
        >
          <image class="content-cover" src="{{item.coverImage}}" mode="aspectFill" />
          <view class="content-info">
            <view class="content-title">{{item.title}}</view>
            <view class="content-summary">{{item.summary}}</view>
            <view class="content-meta">
              <text class="meta-item">{{item.createdAt}}</text>
              <text class="meta-item" wx:if="{{item.unlockType === 1}}">💰 {{item.unlockPrice}}积分</text>
              <text class="meta-item" wx:if="{{item.unlockType === 2}}">👑 VIP</text>
              <text class="meta-item" wx:if="{{item.unlockType === 4}}">🆓 免费</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
