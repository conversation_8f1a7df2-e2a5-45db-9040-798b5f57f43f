const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Favorite = sequelize.define('Favorite', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
      comment: '收藏ID'
    },
    userId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '用户ID'
    },
    contentId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '内容ID'
    }
  }, {
    tableName: 'favorites',
    comment: '收藏表',
    indexes: [
      {
        unique: true,
        fields: ['user_id', 'content_id']
      },
      {
        fields: ['user_id']
      },
      {
        fields: ['content_id']
      },
      {
        fields: ['created_at']
      }
    ]
  });

  return Favorite;
};
