const winston = require('winston');
const path = require('path');
const config = require('../config/config');

// 创建logs目录
const fs = require('fs');
const logDir = path.dirname(config.log.file);
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

// 自定义日志格式
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
  }),
  winston.format.errors({ stack: true }),
  winston.format.printf(({ level, message, timestamp, stack }) => {
    if (stack) {
      return `${timestamp} [${level.toUpperCase()}]: ${message}\n${stack}`;
    }
    return `${timestamp} [${level.toUpperCase()}]: ${message}`;
  })
);

// 创建logger实例
const logger = winston.createLogger({
  level: config.log.level,
  format: logFormat,
  transports: [
    // 文件日志
    new winston.transports.File({
      filename: config.log.file,
      maxsize: 20 * 1024 * 1024, // 20MB
      maxFiles: 5,
      tailable: true
    }),
    // 错误日志单独文件
    new winston.transports.File({
      filename: path.join(logDir, 'error.log'),
      level: 'error',
      maxsize: 20 * 1024 * 1024,
      maxFiles: 5,
      tailable: true
    })
  ]
});

// 开发环境添加控制台输出
if (config.server.env === 'development') {
  logger.add(new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple()
    )
  }));
}

// 封装常用日志方法
const log = {
  info: (message, meta = {}) => {
    logger.info(message, meta);
  },
  
  error: (message, error = null, meta = {}) => {
    if (error instanceof Error) {
      logger.error(message, { ...meta, error: error.message, stack: error.stack });
    } else {
      logger.error(message, meta);
    }
  },
  
  warn: (message, meta = {}) => {
    logger.warn(message, meta);
  },
  
  debug: (message, meta = {}) => {
    logger.debug(message, meta);
  },

  // API请求日志
  request: (ctx, responseTime) => {
    const { method, url, ip, headers } = ctx.request;
    const { status } = ctx.response;
    const userAgent = headers['user-agent'] || '';
    
    logger.info('API Request', {
      method,
      url,
      status,
      ip,
      userAgent,
      responseTime: `${responseTime}ms`,
      userId: ctx.state.user?.id || 'anonymous'
    });
  },

  // 业务操作日志
  business: (action, userId, details = {}) => {
    logger.info('Business Operation', {
      action,
      userId,
      ...details,
      timestamp: new Date().toISOString()
    });
  },

  // 安全相关日志
  security: (event, details = {}) => {
    logger.warn('Security Event', {
      event,
      ...details,
      timestamp: new Date().toISOString()
    });
  }
};

module.exports = log;
