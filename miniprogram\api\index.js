const request = require('../utils/request');

// 认证相关API
const auth = {
  // 微信登录
  wechatLogin: (data) => request.post('/auth/wechat-login', data),
  
  // 验证token
  verify: () => request.get('/auth/verify'),
  
  // 登出
  logout: () => request.post('/auth/logout')
};

// 用户相关API
const user = {
  // 获取用户信息
  getProfile: () => request.get('/user/profile'),
  
  // 更新用户信息
  updateProfile: (data) => request.put('/user/profile', data),
  
  // 获取积分信息
  getPoints: () => request.get('/user/points'),
  
  // 获取积分日志
  getPointLogs: (params) => request.get('/user/point-logs', params),
  
  // 获取解锁记录
  getUnlocks: (params) => request.get('/user/unlocks', params),
  
  // 获取收藏列表
  getFavorites: (params) => request.get('/user/favorites', params),
  
  // 获取邀请信息
  getInviteInfo: () => request.get('/user/invite'),
  
  // 获取邀请记录
  getInviteLogs: (params) => request.get('/user/invite-logs', params),
  
  // 获取签到信息
  getSignInfo: () => request.get('/user/sign-info'),
  
  // 获取VIP信息
  getVipInfo: () => request.get('/user/vip-info')
};

// 内容相关API
const content = {
  // 获取内容列表
  getList: (params) => request.get('/content', params),
  
  // 搜索内容
  search: (params) => request.get('/content/search', params),
  
  // 获取内容详情
  getDetail: (id) => request.get(`/content/${id}`),
  
  // 解锁内容
  unlock: (id) => request.post(`/content/${id}/unlock`),
  
  // 点赞内容
  like: (id) => request.post(`/content/${id}/like`),
  
  // 取消点赞
  unlike: (id) => request.delete(`/content/${id}/like`),
  
  // 收藏内容
  favorite: (id) => request.post(`/content/${id}/favorite`),
  
  // 取消收藏
  unfavorite: (id) => request.delete(`/content/${id}/favorite`),
  
  // 分享内容
  share: (id, data) => request.post(`/content/${id}/share`, data),
  
  // 获取评论列表
  getComments: (id, params) => request.get(`/content/${id}/comments`, params),
  
  // 发表评论
  addComment: (id, data) => request.post(`/content/${id}/comments`, data),
  
  // 点赞评论
  likeComment: (commentId) => request.post(`/content/comments/${commentId}/like`),
  
  // 取消评论点赞
  unlikeComment: (commentId) => request.delete(`/content/comments/${commentId}/like`),
  
  // 删除评论
  deleteComment: (commentId) => request.delete(`/content/comments/${commentId}`),
  
  // 获取相关推荐
  getRelated: (id, params) => request.get(`/content/${id}/related`, params)
};

// 积分相关API
const point = {
  // 每日签到
  dailySign: () => request.post('/point/sign'),
  
  // 观看广告
  watchAd: (data) => request.post('/point/watch-ad', data),
  
  // 卡密兑换
  cardExchange: (data) => request.post('/point/card-exchange', data),
  
  // 获取积分日志
  getLogs: (params) => request.get('/point/logs', params),
  
  // 获取积分统计
  getStats: (params) => request.get('/point/stats', params),
  
  // 获取积分配置
  getConfig: () => request.get('/point/config'),
  
  // 获取今日积分获取情况
  getTodayStats: () => request.get('/point/today'),
  
  // 获取积分来源统计
  getSourceStats: (params) => request.get('/point/source-stats', params)
};

// 公开API
const publicApi = {
  // 获取分类列表
  getCategories: () => request.get('/public/categories'),
  
  // 获取标签列表
  getTags: (params) => request.get('/public/tags', params),
  
  // 获取热门内容
  getHotContents: (params) => request.get('/public/hot', params),
  
  // 获取推荐内容
  getRecommendContents: (params) => request.get('/public/recommend', params),
  
  // 获取最新内容
  getLatestContents: (params) => request.get('/public/latest', params),
  
  // 获取首页数据
  getHomeData: () => request.get('/public/home'),
  
  // 获取系统配置
  getSystemConfig: () => request.get('/public/config'),
  
  // 获取统计信息
  getPublicStats: () => request.get('/public/stats')
};

// 微信相关API
const wechat = {
  // 生成分享二维码
  generateQRCode: (data) => request.post('/wechat/qrcode', data),
  
  // 微信支付
  createPayment: (data) => request.post('/wechat/pay', data)
};

module.exports = {
  auth,
  user,
  content,
  point,
  public: publicApi,
  wechat
};
