const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const PointLog = sequelize.define('PointLog', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
      comment: '日志ID'
    },
    userId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '用户ID'
    },
    type: {
      type: DataTypes.ENUM(
        'gain', 'consume', 'admin_adjust', 'expire', 'refund'
      ),
      allowNull: false,
      comment: '类型：gain获取 consume消耗 admin_adjust管理员调整 expire过期 refund退款'
    },
    source: {
      type: DataTypes.ENUM(
        'daily_sign', 'watch_ad', 'invite_register', 'invite_unlock', 
        'share_content', 'content_creation', 'complete_profile', 'first_unlock',
        'unlock_content', 'card_exchange', 'admin_adjust', 'system_expire',
        'refund_unlock', 'activity_reward', 'other'
      ),
      allowNull: false,
      comment: '来源'
    },
    amount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '积分变动数量（正数为增加，负数为减少）'
    },
    balance: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '变动后余额'
    },
    description: {
      type: DataTypes.STRING(500),
      allowNull: true,
      comment: '描述'
    },
    relatedId: {
      type: DataTypes.BIGINT,
      allowNull: true,
      comment: '关联ID（如内容ID、卡密ID等）'
    },
    relatedType: {
      type: DataTypes.STRING(50),
      allowNull: true,
      comment: '关联类型（如content、card等）'
    },
    ip: {
      type: DataTypes.STRING(45),
      allowNull: true,
      comment: '操作IP'
    },
    userAgent: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '用户代理'
    },
    adminId: {
      type: DataTypes.BIGINT,
      allowNull: true,
      comment: '管理员ID（管理员操作时记录）'
    }
  }, {
    tableName: 'point_logs',
    comment: '积分日志表',
    indexes: [
      {
        fields: ['user_id', 'created_at']
      },
      {
        fields: ['type']
      },
      {
        fields: ['source']
      },
      {
        fields: ['created_at']
      },
      {
        fields: ['related_id', 'related_type']
      }
    ]
  });

  // 实例方法
  PointLog.prototype.toJSON = function() {
    const values = { ...this.get() };
    
    // 格式化显示
    values.isGain = values.amount > 0;
    values.displayAmount = Math.abs(values.amount);
    values.displayType = values.amount > 0 ? '获得' : '消耗';
    
    return values;
  };

  // 类方法
  // 创建积分日志
  PointLog.createLog = async function(data) {
    const {
      userId,
      type,
      source,
      amount,
      balance,
      description,
      relatedId,
      relatedType,
      ip,
      userAgent,
      adminId
    } = data;

    return await this.create({
      userId,
      type,
      source,
      amount,
      balance,
      description,
      relatedId,
      relatedType,
      ip,
      userAgent,
      adminId
    });
  };

  // 获取用户积分日志
  PointLog.getUserLogs = function(userId, page = 1, pageSize = 20, type = null) {
    const offset = (page - 1) * pageSize;
    const where = { userId };
    
    if (type) {
      where.type = type;
    }

    return this.findAndCountAll({
      where,
      order: [['createdAt', 'DESC']],
      limit: pageSize,
      offset,
      include: [
        {
          model: sequelize.models.User,
          as: 'user',
          attributes: ['id', 'nickname', 'avatar']
        }
      ]
    });
  };

  // 获取用户今日积分获取统计
  PointLog.getTodayGainStats = async function(userId) {
    const { Op } = require('sequelize');
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const result = await this.findAll({
      where: {
        userId,
        type: 'gain',
        createdAt: {
          [Op.gte]: today,
          [Op.lt]: tomorrow
        }
      },
      attributes: [
        'source',
        [sequelize.fn('SUM', sequelize.col('amount')), 'totalAmount'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      group: ['source']
    });

    const stats = {};
    let totalGain = 0;

    result.forEach(item => {
      const source = item.get('source');
      const amount = parseInt(item.get('totalAmount')) || 0;
      const count = parseInt(item.get('count')) || 0;
      
      stats[source] = { amount, count };
      totalGain += amount;
    });

    return {
      stats,
      totalGain,
      date: today.toISOString().split('T')[0]
    };
  };

  // 获取用户积分统计
  PointLog.getUserStats = async function(userId, days = 30) {
    const { Op } = require('sequelize');
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const result = await this.findAll({
      where: {
        userId,
        createdAt: {
          [Op.gte]: startDate
        }
      },
      attributes: [
        'type',
        [sequelize.fn('SUM', sequelize.col('amount')), 'totalAmount'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      group: ['type']
    });

    const stats = {
      gain: { amount: 0, count: 0 },
      consume: { amount: 0, count: 0 }
    };

    result.forEach(item => {
      const type = item.get('type');
      const amount = Math.abs(parseInt(item.get('totalAmount')) || 0);
      const count = parseInt(item.get('count')) || 0;
      
      if (stats[type]) {
        stats[type] = { amount, count };
      }
    });

    return {
      ...stats,
      period: `${days}天`,
      startDate: startDate.toISOString().split('T')[0]
    };
  };

  // 获取积分来源统计
  PointLog.getSourceStats = async function(userId, days = 30) {
    const { Op } = require('sequelize');
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const result = await this.findAll({
      where: {
        userId,
        type: 'gain',
        createdAt: {
          [Op.gte]: startDate
        }
      },
      attributes: [
        'source',
        [sequelize.fn('SUM', sequelize.col('amount')), 'totalAmount'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      group: ['source'],
      order: [[sequelize.fn('SUM', sequelize.col('amount')), 'DESC']]
    });

    return result.map(item => ({
      source: item.get('source'),
      amount: parseInt(item.get('totalAmount')) || 0,
      count: parseInt(item.get('count')) || 0
    }));
  };

  // 获取系统积分统计（管理后台用）
  PointLog.getSystemStats = async function(days = 30) {
    const { Op } = require('sequelize');
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const result = await this.findAll({
      where: {
        createdAt: {
          [Op.gte]: startDate
        }
      },
      attributes: [
        'type',
        'source',
        [sequelize.fn('SUM', sequelize.col('amount')), 'totalAmount'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
        [sequelize.fn('COUNT', sequelize.fn('DISTINCT', sequelize.col('user_id'))), 'userCount']
      ],
      group: ['type', 'source'],
      order: [['type', 'ASC'], [sequelize.fn('SUM', sequelize.col('amount')), 'DESC']]
    });

    const stats = {
      gain: {},
      consume: {},
      totalGain: 0,
      totalConsume: 0,
      activeUsers: 0
    };

    result.forEach(item => {
      const type = item.get('type');
      const source = item.get('source');
      const amount = Math.abs(parseInt(item.get('totalAmount')) || 0);
      const count = parseInt(item.get('count')) || 0;
      const userCount = parseInt(item.get('userCount')) || 0;

      if (!stats[type]) {
        stats[type] = {};
      }

      stats[type][source] = { amount, count, userCount };

      if (type === 'gain') {
        stats.totalGain += amount;
      } else if (type === 'consume') {
        stats.totalConsume += amount;
      }
    });

    // 计算活跃用户数
    const activeUsersResult = await this.findOne({
      where: {
        createdAt: {
          [Op.gte]: startDate
        }
      },
      attributes: [
        [sequelize.fn('COUNT', sequelize.fn('DISTINCT', sequelize.col('user_id'))), 'count']
      ]
    });

    stats.activeUsers = parseInt(activeUsersResult.get('count')) || 0;

    return stats;
  };

  return PointLog;
};
