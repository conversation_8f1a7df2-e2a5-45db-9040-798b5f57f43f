const api = require('./api/index');

App({
  globalData: {
    userInfo: null,
    token: null,
    systemInfo: null,
    baseUrl: 'http://localhost:3000/api/v1'
  },

  onLaunch(options) {
    console.log('小程序启动', options);
    
    // 获取系统信息
    this.getSystemInfo();
    
    // 检查登录状态
    this.checkLoginStatus();
    
    // 处理分享参数
    this.handleShareParams(options);
  },

  onShow(options) {
    console.log('小程序显示', options);
    this.handleShareParams(options);
  },

  onHide() {
    console.log('小程序隐藏');
  },

  onError(msg) {
    console.error('小程序错误', msg);
  },

  // 获取系统信息
  getSystemInfo() {
    wx.getSystemInfo({
      success: (res) => {
        this.globalData.systemInfo = res;
        console.log('系统信息', res);
      }
    });
  },

  // 检查登录状态
  checkLoginStatus() {
    const token = wx.getStorageSync('token');
    const userInfo = wx.getStorageSync('userInfo');
    
    if (token && userInfo) {
      this.globalData.token = token;
      this.globalData.userInfo = userInfo;
      
      // 验证token有效性
      this.verifyToken();
    }
  },

  // 验证token
  async verifyToken() {
    try {
      const result = await api.auth.verify();
      if (result.code === 200) {
        this.globalData.userInfo = result.data.user;
        wx.setStorageSync('userInfo', result.data.user);
      } else {
        this.logout();
      }
    } catch (error) {
      console.error('验证token失败', error);
      this.logout();
    }
  },

  // 处理分享参数
  handleShareParams(options) {
    const { scene, query } = options;
    
    // 处理邀请码
    if (query && query.inviteCode) {
      wx.setStorageSync('inviteCode', query.inviteCode);
    }
    
    // 处理内容分享
    if (query && query.contentId) {
      wx.setStorageSync('shareContentId', query.contentId);
      if (query.from) {
        wx.setStorageSync('shareFrom', query.from);
      }
    }
  },

  // 登录
  async login(userInfo) {
    try {
      wx.showLoading({ title: '登录中...' });
      
      const loginResult = await wx.login();
      const inviteCode = wx.getStorageSync('inviteCode');
      
      const result = await api.auth.wechatLogin({
        code: loginResult.code,
        userInfo,
        inviteCode
      });
      
      if (result.code === 200) {
        this.globalData.token = result.data.token.accessToken;
        this.globalData.userInfo = result.data.user;
        
        // 保存到本地存储
        wx.setStorageSync('token', result.data.token.accessToken);
        wx.setStorageSync('userInfo', result.data.user);
        
        // 清除邀请码
        wx.removeStorageSync('inviteCode');
        
        wx.showToast({ title: '登录成功', icon: 'success' });
        return result.data;
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      console.error('登录失败', error);
      wx.showToast({ title: '登录失败', icon: 'none' });
      throw error;
    } finally {
      wx.hideLoading();
    }
  },

  // 登出
  logout() {
    this.globalData.token = null;
    this.globalData.userInfo = null;
    
    wx.removeStorageSync('token');
    wx.removeStorageSync('userInfo');
    
    // 跳转到登录页
    wx.reLaunch({
      url: '/pages/auth/login'
    });
  },

  // 检查是否已登录
  isLoggedIn() {
    return !!(this.globalData.token && this.globalData.userInfo);
  },

  // 获取用户信息
  getUserInfo() {
    return this.globalData.userInfo;
  },

  // 获取token
  getToken() {
    return this.globalData.token;
  },

  // 更新用户信息
  updateUserInfo(userInfo) {
    this.globalData.userInfo = { ...this.globalData.userInfo, ...userInfo };
    wx.setStorageSync('userInfo', this.globalData.userInfo);
  },

  // 显示错误信息
  showError(message) {
    wx.showToast({
      title: message || '操作失败',
      icon: 'none',
      duration: 2000
    });
  },

  // 显示成功信息
  showSuccess(message) {
    wx.showToast({
      title: message || '操作成功',
      icon: 'success',
      duration: 2000
    });
  },

  // 确认对话框
  showConfirm(options) {
    return new Promise((resolve) => {
      wx.showModal({
        title: options.title || '提示',
        content: options.content || '',
        confirmText: options.confirmText || '确定',
        cancelText: options.cancelText || '取消',
        success: (res) => {
          resolve(res.confirm);
        }
      });
    });
  },

  // 分享配置
  getShareConfig(title, path, imageUrl) {
    const userInfo = this.getUserInfo();
    const inviteCode = userInfo ? userInfo.inviteCode : '';
    
    return {
      title: title || '知识付费平台 - 优质学习资源分享',
      path: path ? `${path}?inviteCode=${inviteCode}` : `/pages/index/index?inviteCode=${inviteCode}`,
      imageUrl: imageUrl || '/images/share-default.jpg'
    };
  }
});
