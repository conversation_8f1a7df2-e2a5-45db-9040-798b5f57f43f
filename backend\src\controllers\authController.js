const jwt = require('jsonwebtoken');
const { User } = require('../models');
const config = require('../config/config');
const logger = require('../utils/logger');
const { BusinessError, ValidationError } = require('../middleware/errorHandler');

class AuthController {
  // 用户注册
  async register(ctx) {
    const { mobile, nickname, inviteCode } = ctx.request.body;

    // 验证必填字段
    if (!mobile || !nickname) {
      throw new ValidationError('手机号和昵称不能为空');
    }

    // 检查手机号是否已注册
    const existingUser = await User.findByMobile(mobile);
    if (existingUser) {
      throw new BusinessError('手机号已注册');
    }

    // 查找邀请人
    let inviter = null;
    if (inviteCode) {
      inviter = await User.findByInviteCode(inviteCode);
      if (!inviter) {
        throw new BusinessError('邀请码无效');
      }
    }

    // 创建用户
    const user = await User.create({
      mobile,
      nickname,
      inviterId: inviter ? inviter.id : null,
      points: config.points.other.completeProfile // 注册奖励积分
    });

    // 记录邀请关系
    if (inviter) {
      const { InviteLog } = require('../models');
      await InviteLog.create({
        inviterId: inviter.id,
        inviteeId: user.id,
        rewardPoints: config.points.invite.register
      });

      // 给邀请人奖励积分
      await inviter.addPoints(
        config.points.invite.register,
        `邀请用户${user.nickname}注册`
      );
    }

    // 生成JWT令牌
    const token = this.generateToken(user);

    logger.business('用户注册', user.id, { mobile, inviteCode });

    ctx.body = {
      code: 200,
      message: '注册成功',
      data: {
        user: user.toJSON(),
        token
      }
    };
  }

  // 用户登录
  async login(ctx) {
    const { mobile, code } = ctx.request.body;

    if (!mobile || !code) {
      throw new ValidationError('手机号和验证码不能为空');
    }

    // TODO: 验证短信验证码
    // 这里简化处理，实际应该验证短信验证码
    if (code !== '123456') {
      throw new BusinessError('验证码错误');
    }

    // 查找用户
    let user = await User.findByMobile(mobile);
    if (!user) {
      // 用户不存在，自动注册
      user = await User.create({
        mobile,
        nickname: `用户${mobile.substr(-4)}`
      });
    }

    // 更新登录信息
    await user.updateLoginInfo(ctx.ip);

    // 生成JWT令牌
    const token = this.generateToken(user);

    logger.business('用户登录', user.id, { mobile, ip: ctx.ip });

    ctx.body = {
      code: 200,
      message: '登录成功',
      data: {
        user: user.toJSON(),
        token
      }
    };
  }

  // 微信登录
  async wechatLogin(ctx) {
    const { code, userInfo, inviteCode } = ctx.request.body;

    if (!code) {
      throw new ValidationError('微信授权码不能为空');
    }

    // TODO: 调用微信API获取openid
    // 这里简化处理
    const openid = `mock_openid_${Date.now()}`;

    // 查找用户
    let user = await User.findByOpenid(openid);
    if (!user) {
      // 查找邀请人
      let inviter = null;
      if (inviteCode) {
        inviter = await User.findByInviteCode(inviteCode);
      }

      // 创建新用户
      user = await User.create({
        openid,
        nickname: userInfo?.nickName || '微信用户',
        avatar: userInfo?.avatarUrl,
        gender: userInfo?.gender || 0,
        inviterId: inviter ? inviter.id : null,
        points: config.points.other.completeProfile
      });

      // 记录邀请关系
      if (inviter) {
        const { InviteLog } = require('../models');
        await InviteLog.create({
          inviterId: inviter.id,
          inviteeId: user.id,
          rewardPoints: config.points.invite.register
        });

        // 给邀请人奖励积分
        await inviter.addPoints(
          config.points.invite.register,
          `邀请用户${user.nickname}注册`
        );
      }
    } else {
      // 更新用户信息
      if (userInfo) {
        user.nickname = userInfo.nickName || user.nickname;
        user.avatar = userInfo.avatarUrl || user.avatar;
        user.gender = userInfo.gender || user.gender;
        await user.save();
      }
    }

    // 更新登录信息
    await user.updateLoginInfo(ctx.ip);

    // 生成JWT令牌
    const token = this.generateToken(user);

    logger.business('微信登录', user.id, { openid, ip: ctx.ip });

    ctx.body = {
      code: 200,
      message: '登录成功',
      data: {
        user: user.toJSON(),
        token
      }
    };
  }

  // 发送短信验证码
  async sendSms(ctx) {
    const { mobile } = ctx.request.body;

    if (!mobile) {
      throw new ValidationError('手机号不能为空');
    }

    // TODO: 调用短信服务发送验证码
    // 这里简化处理
    logger.info(`发送验证码到 ${mobile}: 123456`);

    ctx.body = {
      code: 200,
      message: '验证码发送成功'
    };
  }

  // 刷新令牌
  async refreshToken(ctx) {
    const { refreshToken } = ctx.request.body;

    if (!refreshToken) {
      throw new ValidationError('刷新令牌不能为空');
    }

    try {
      const decoded = jwt.verify(refreshToken, config.jwt.secret);
      const user = await User.findByPk(decoded.userId);

      if (!user) {
        throw new BusinessError('用户不存在');
      }

      const token = this.generateToken(user);

      ctx.body = {
        code: 200,
        message: '令牌刷新成功',
        data: { token }
      };
    } catch (error) {
      throw new BusinessError('刷新令牌无效');
    }
  }

  // 用户登出
  async logout(ctx) {
    // JWT是无状态的，这里只是记录日志
    const userId = ctx.state.user?.id;
    
    if (userId) {
      logger.business('用户登出', userId, { ip: ctx.ip });
    }

    ctx.body = {
      code: 200,
      message: '登出成功'
    };
  }

  // 验证令牌
  async verifyToken(ctx) {
    const user = ctx.state.user;

    ctx.body = {
      code: 200,
      message: '令牌有效',
      data: { user }
    };
  }

  // 生成JWT令牌
  generateToken(user) {
    const payload = {
      userId: user.id,
      openid: user.openid,
      mobile: user.mobile
    };

    const token = jwt.sign(payload, config.jwt.secret, {
      expiresIn: config.jwt.expiresIn
    });

    const refreshToken = jwt.sign(payload, config.jwt.secret, {
      expiresIn: config.jwt.refreshExpiresIn
    });

    return {
      accessToken: token,
      refreshToken,
      expiresIn: config.jwt.expiresIn
    };
  }
}

module.exports = new AuthController();
