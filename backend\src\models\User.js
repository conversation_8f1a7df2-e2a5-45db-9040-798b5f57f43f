const { DataTypes } = require('sequelize');
const bcrypt = require('bcryptjs');

module.exports = (sequelize) => {
  const User = sequelize.define('User', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
      comment: '用户ID'
    },
    openid: {
      type: DataTypes.STRING(64),
      unique: true,
      allowNull: true,
      comment: '微信openid'
    },
    unionid: {
      type: DataTypes.STRING(64),
      unique: true,
      allowNull: true,
      comment: '微信unionid'
    },
    mobile: {
      type: DataTypes.STRING(20),
      unique: true,
      allowNull: true,
      comment: '手机号'
    },
    nickname: {
      type: DataTypes.STRING(50),
      allowNull: false,
      defaultValue: '用户',
      comment: '昵称'
    },
    avatar: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '头像URL'
    },
    gender: {
      type: DataTypes.TINYINT,
      allowNull: true,
      defaultValue: 0,
      comment: '性别：0未知 1男 2女'
    },
    birthday: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      comment: '生日'
    },
    points: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '当前积分'
    },
    totalPoints: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '累计获得积分'
    },
    vipLevel: {
      type: DataTypes.TINYINT,
      allowNull: false,
      defaultValue: 0,
      comment: 'VIP等级：0普通用户 1VIP1 2VIP2'
    },
    vipExpireTime: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'VIP到期时间'
    },
    inviterId: {
      type: DataTypes.BIGINT,
      allowNull: true,
      comment: '邀请人ID'
    },
    inviteCode: {
      type: DataTypes.STRING(20),
      unique: true,
      allowNull: false,
      comment: '邀请码'
    },
    inviteCount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '邀请人数'
    },
    status: {
      type: DataTypes.TINYINT,
      allowNull: false,
      defaultValue: 1,
      comment: '状态：1正常 2封禁 3注销'
    },
    lastLoginTime: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '最后登录时间'
    },
    lastLoginIp: {
      type: DataTypes.STRING(45),
      allowNull: true,
      comment: '最后登录IP'
    },
    loginCount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '登录次数'
    },
    remark: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '备注'
    }
  }, {
    tableName: 'users',
    comment: '用户表',
    indexes: [
      {
        unique: true,
        fields: ['openid']
      },
      {
        unique: true,
        fields: ['mobile']
      },
      {
        unique: true,
        fields: ['invite_code']
      },
      {
        fields: ['inviter_id']
      },
      {
        fields: ['status']
      },
      {
        fields: ['vip_level']
      },
      {
        fields: ['created_at']
      }
    ],
    hooks: {
      // 创建用户前生成邀请码
      beforeCreate: async (user) => {
        if (!user.inviteCode) {
          user.inviteCode = await generateInviteCode();
        }
      }
    }
  });

  // 实例方法
  User.prototype.toJSON = function() {
    const values = { ...this.get() };
    // 移除敏感信息
    delete values.remark;
    return values;
  };

  // 检查是否为VIP
  User.prototype.isVip = function() {
    return this.vipLevel > 0 && this.vipExpireTime && new Date() < this.vipExpireTime;
  };

  // 获取VIP剩余天数
  User.prototype.getVipRemainingDays = function() {
    if (!this.isVip()) return 0;
    const now = new Date();
    const expireTime = new Date(this.vipExpireTime);
    const diffTime = expireTime - now;
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  // 更新登录信息
  User.prototype.updateLoginInfo = async function(ip) {
    this.lastLoginTime = new Date();
    this.lastLoginIp = ip;
    this.loginCount += 1;
    await this.save();
  };

  // 增加积分
  User.prototype.addPoints = async function(points, description = '') {
    const transaction = await sequelize.transaction();
    try {
      // 更新用户积分
      this.points += points;
      this.totalPoints += points;
      await this.save({ transaction });

      // 记录积分日志
      const PointLog = sequelize.models.PointLog;
      await PointLog.create({
        userId: this.id,
        type: 'gain',
        amount: points,
        balance: this.points,
        description
      }, { transaction });

      await transaction.commit();
      return this.points;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  };

  // 扣除积分
  User.prototype.deductPoints = async function(points, description = '') {
    if (this.points < points) {
      throw new Error('积分不足');
    }

    const transaction = await sequelize.transaction();
    try {
      // 更新用户积分
      this.points -= points;
      await this.save({ transaction });

      // 记录积分日志
      const PointLog = sequelize.models.PointLog;
      await PointLog.create({
        userId: this.id,
        type: 'consume',
        amount: -points,
        balance: this.points,
        description
      }, { transaction });

      await transaction.commit();
      return this.points;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  };

  // 类方法
  // 根据openid查找用户
  User.findByOpenid = function(openid) {
    return this.findOne({ where: { openid } });
  };

  // 根据手机号查找用户
  User.findByMobile = function(mobile) {
    return this.findOne({ where: { mobile } });
  };

  // 根据邀请码查找用户
  User.findByInviteCode = function(inviteCode) {
    return this.findOne({ where: { inviteCode } });
  };

  return User;
};

// 生成唯一邀请码
async function generateInviteCode() {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let code;
  let exists = true;
  
  while (exists) {
    code = '';
    for (let i = 0; i < 8; i++) {
      code += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    
    // 检查是否已存在
    const User = require('./index').User;
    const existingUser = await User.findOne({ where: { inviteCode: code } });
    exists = !!existingUser;
  }
  
  return code;
}
