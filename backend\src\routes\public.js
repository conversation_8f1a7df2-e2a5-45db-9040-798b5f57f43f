const Router = require('koa-router');
const publicController = require('../controllers/publicController');

const router = new Router();

// 获取分类列表
router.get('/categories', publicController.getCategories);

// 获取标签列表
router.get('/tags', publicController.getTags);

// 获取热门内容
router.get('/hot', publicController.getHotContents);

// 获取推荐内容
router.get('/recommend', publicController.getRecommendContents);

// 获取最新内容
router.get('/latest', publicController.getLatestContents);

// 获取首页数据
router.get('/home', publicController.getHomeData);

// 获取系统配置
router.get('/config', publicController.getSystemConfig);

// 获取统计信息
router.get('/stats', publicController.getPublicStats);

module.exports = router;
