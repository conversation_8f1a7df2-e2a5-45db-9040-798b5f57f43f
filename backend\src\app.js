const Koa = require('koa');
const bodyParser = require('koa-bodyparser');
const cors = require('koa-cors');
const helmet = require('helmet');
const jwt = require('koa-jwt');
const path = require('path');
require('dotenv').config();

const config = require('./config/config');
const logger = require('./utils/logger');
const errorHandler = require('./middleware/errorHandler');
const rateLimiter = require('./middleware/rateLimiter');
const routes = require('./routes');

// 创建Koa应用
const app = new Koa();

// 全局错误处理
app.use(errorHandler);

// 安全中间件
app.use(helmet());

// 跨域处理
app.use(cors({
  origin: config.cors.origin,
  credentials: true,
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization', 'Accept']
}));

// 请求体解析
app.use(bodyParser({
  enableTypes: ['json', 'form'],
  jsonLimit: '10mb',
  formLimit: '10mb'
}));

// 限流中间件
app.use(rateLimiter);

// JWT认证中间件（排除公开路由）
app.use(jwt({
  secret: config.jwt.secret,
  algorithms: ['HS256']
}).unless({
  path: [
    /^\/api\/v1\/auth/,
    /^\/api\/v1\/public/,
    /^\/api\/v1\/wechat/,
    /^\/docs/,
    /^\/health/
  ]
}));

// 路由
app.use(routes.routes());
app.use(routes.allowedMethods());

// 健康检查
app.use(async (ctx, next) => {
  if (ctx.path === '/health') {
    ctx.body = {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime()
    };
    return;
  }
  await next();
});

// 404处理
app.use(async (ctx) => {
  ctx.status = 404;
  ctx.body = {
    code: 404,
    message: 'Not Found',
    timestamp: new Date().toISOString()
  };
});

// 启动服务器
const PORT = config.server.port || 3000;
const HOST = config.server.host || '0.0.0.0';

app.listen(PORT, HOST, () => {
  logger.info(`🚀 服务器启动成功`);
  logger.info(`📍 地址: http://${HOST}:${PORT}`);
  logger.info(`🌍 环境: ${process.env.NODE_ENV || 'development'}`);
  logger.info(`📖 API文档: http://${HOST}:${PORT}/docs`);
});

// 优雅关闭
process.on('SIGTERM', () => {
  logger.info('收到SIGTERM信号，正在关闭服务器...');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('收到SIGINT信号，正在关闭服务器...');
  process.exit(0);
});

// 未捕获异常处理
process.on('uncaughtException', (err) => {
  logger.error('未捕获的异常:', err);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('未处理的Promise拒绝:', reason);
  process.exit(1);
});

module.exports = app;
