const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const SignLog = sequelize.define('SignLog', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
      comment: '签到记录ID'
    },
    userId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '用户ID'
    },
    signDate: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      comment: '签到日期'
    },
    points: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '获得积分'
    },
    continuousDays: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
      comment: '连续签到天数'
    },
    ip: {
      type: DataTypes.STRING(45),
      allowNull: true,
      comment: '签到IP'
    }
  }, {
    tableName: 'sign_logs',
    comment: '签到记录表',
    indexes: [
      {
        unique: true,
        fields: ['user_id', 'sign_date']
      },
      {
        fields: ['sign_date']
      }
    ]
  });

  return SignLog;
};
