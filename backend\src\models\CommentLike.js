const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const CommentLike = sequelize.define('CommentLike', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
      comment: '点赞ID'
    },
    commentId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '评论ID'
    },
    userId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '用户ID'
    }
  }, {
    tableName: 'comment_likes',
    comment: '评论点赞表',
    indexes: [
      {
        unique: true,
        fields: ['comment_id', 'user_id']
      },
      {
        fields: ['comment_id']
      },
      {
        fields: ['user_id']
      }
    ]
  });

  return CommentLike;
};
