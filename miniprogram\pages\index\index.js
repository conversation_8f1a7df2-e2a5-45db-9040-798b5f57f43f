const api = require('../../api/index');
const app = getApp();

Page({
  data: {
    loading: true,
    banners: [],
    categories: [],
    hotContents: [],
    recommendContents: [],
    latestContents: [],
    stats: {},
    currentBanner: 0
  },

  onLoad(options) {
    console.log('首页加载', options);
    this.loadHomeData();
    
    // 处理分享参数
    this.handleShareParams(options);
  },

  onShow() {
    // 每次显示时刷新用户信息
    if (app.isLoggedIn()) {
      this.refreshUserInfo();
    }
  },

  onPullDownRefresh() {
    this.loadHomeData().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 加载首页数据
  async loadHomeData() {
    try {
      wx.showLoading({ title: '加载中...' });
      
      const [homeData, stats] = await Promise.all([
        api.public.getHomeData(),
        api.public.getPublicStats()
      ]);
      
      if (homeData.code === 200) {
        this.setData({
          banners: homeData.data.banners || [],
          categories: homeData.data.categories || [],
          hotContents: homeData.data.hotContents || [],
          recommendContents: homeData.data.recommendContents || [],
          latestContents: homeData.data.latestContents || []
        });
      }
      
      if (stats.code === 200) {
        this.setData({
          stats: stats.data
        });
      }
    } catch (error) {
      console.error('加载首页数据失败', error);
      app.showError('加载失败，请重试');
    } finally {
      this.setData({ loading: false });
      wx.hideLoading();
    }
  },

  // 刷新用户信息
  async refreshUserInfo() {
    try {
      const result = await api.user.getProfile();
      if (result.code === 200) {
        app.updateUserInfo(result.data);
      }
    } catch (error) {
      console.error('刷新用户信息失败', error);
    }
  },

  // 处理分享参数
  handleShareParams(options) {
    if (options.inviteCode) {
      wx.setStorageSync('inviteCode', options.inviteCode);
    }
  },

  // 轮播图切换
  onBannerChange(e) {
    this.setData({
      currentBanner: e.detail.current
    });
  },

  // 点击轮播图
  onBannerTap(e) {
    const { index } = e.currentTarget.dataset;
    const banner = this.data.banners[index];
    
    if (banner.link) {
      if (banner.link.startsWith('/pages/')) {
        wx.navigateTo({ url: banner.link });
      } else if (banner.link.startsWith('http')) {
        // 外链处理
        wx.setClipboardData({
          data: banner.link,
          success: () => {
            app.showSuccess('链接已复制到剪贴板');
          }
        });
      }
    }
  },

  // 点击分类
  onCategoryTap(e) {
    const { category } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/category/category?categoryId=${category.id}&categoryName=${category.name}`
    });
  },

  // 点击内容
  onContentTap(e) {
    const { content } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/content/detail?id=${content.id}`
    });
  },

  // 查看更多热门内容
  onViewMoreHot() {
    wx.navigateTo({
      url: '/pages/category/category?sort=hot'
    });
  },

  // 查看更多推荐内容
  onViewMoreRecommend() {
    wx.navigateTo({
      url: '/pages/category/category?sort=recommend'
    });
  },

  // 查看更多最新内容
  onViewMoreLatest() {
    wx.navigateTo({
      url: '/pages/category/category?sort=latest'
    });
  },

  // 去签到
  onSignTap() {
    if (!app.isLoggedIn()) {
      wx.navigateTo({ url: '/pages/auth/login' });
      return;
    }
    
    wx.navigateTo({ url: '/pages/point/sign' });
  },

  // 去搜索
  onSearchTap() {
    wx.navigateTo({ url: '/pages/search/search' });
  },

  // 分享配置
  onShareAppMessage() {
    return app.getShareConfig(
      '知识付费平台 - 优质学习资源分享',
      '/pages/index/index'
    );
  },

  onShareTimeline() {
    return app.getShareConfig(
      '知识付费平台 - 优质学习资源分享',
      '/pages/index/index'
    );
  }
});
