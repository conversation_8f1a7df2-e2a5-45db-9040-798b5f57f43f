const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const ContentTag = sequelize.define('ContentTag', {
    contentId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '内容ID'
    },
    tagId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '标签ID'
    }
  }, {
    tableName: 'content_tags',
    comment: '内容标签关联表',
    timestamps: false,
    indexes: [
      {
        unique: true,
        fields: ['content_id', 'tag_id']
      },
      {
        fields: ['content_id']
      },
      {
        fields: ['tag_id']
      }
    ]
  });

  return ContentTag;
};
