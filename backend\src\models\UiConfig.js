const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const UiConfig = sequelize.define('UiConfig', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      comment: '配置ID'
    },
    key: {
      type: DataTypes.STRING(50),
      unique: true,
      allowNull: false,
      comment: '配置键'
    },
    value: {
      type: DataTypes.TEXT,
      allowNull: false,
      comment: '配置值'
    },
    description: {
      type: DataTypes.STRING(200),
      allowNull: true,
      comment: '配置描述'
    },
    updatedBy: {
      type: DataTypes.BIGINT,
      allowNull: true,
      comment: '更新人'
    }
  }, {
    tableName: 'ui_config',
    comment: 'UI配置表',
    indexes: [
      {
        unique: true,
        fields: ['key']
      }
    ]
  });

  return UiConfig;
};
