const Router = require('koa-router');
const pointController = require('../controllers/pointController');
const { pointsRateLimit } = require('../middleware/rateLimiter');

const router = new Router();

// 每日签到
router.post('/sign', pointsRateLimit, pointController.dailySign);

// 观看广告
router.post('/watch-ad', pointsRateLimit, pointController.watchAd);

// 卡密兑换
router.post('/card-exchange', pointController.cardExchange);

// 获取积分日志
router.get('/logs', pointController.getLogs);

// 获取积分统计
router.get('/stats', pointController.getStats);

// 获取积分配置
router.get('/config', pointController.getConfig);

// 获取今日积分获取情况
router.get('/today', pointController.getTodayStats);

// 获取积分来源统计
router.get('/source-stats', pointController.getSourceStats);

module.exports = router;
