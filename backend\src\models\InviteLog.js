const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const InviteLog = sequelize.define('InviteLog', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
      comment: '邀请记录ID'
    },
    inviterId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '邀请人ID'
    },
    inviteeId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '被邀请人ID'
    },
    rewardPoints: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '奖励积分'
    },
    status: {
      type: DataTypes.TINYINT,
      allowNull: false,
      defaultValue: 1,
      comment: '状态：1有效 0无效'
    }
  }, {
    tableName: 'invite_logs',
    comment: '邀请记录表',
    indexes: [
      {
        fields: ['inviter_id']
      },
      {
        fields: ['invitee_id']
      },
      {
        fields: ['created_at']
      }
    ]
  });

  return InviteLog;
};
