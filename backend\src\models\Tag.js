const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Tag = sequelize.define('Tag', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      comment: '标签ID'
    },
    name: {
      type: DataTypes.STRING(50),
      unique: true,
      allowNull: false,
      comment: '标签名称'
    },
    color: {
      type: DataTypes.STRING(7),
      allowNull: true,
      defaultValue: '#1890ff',
      comment: '标签颜色'
    },
    sortOrder: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '排序权重'
    },
    contentCount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '关联内容数量'
    }
  }, {
    tableName: 'tags',
    comment: '标签表',
    indexes: [
      {
        unique: true,
        fields: ['name']
      },
      {
        fields: ['sort_order']
      }
    ]
  });

  return Tag;
};
