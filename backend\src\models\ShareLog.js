const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const ShareLog = sequelize.define('ShareLog', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
      comment: '分享记录ID'
    },
    userId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '分享用户ID'
    },
    contentId: {
      type: DataTypes.BIGINT,
      allowNull: true,
      comment: '分享内容ID'
    },
    shareType: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '分享类型：wechat微信 moments朋友圈 qq QQ等'
    },
    clickCount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '点击次数'
    },
    rewardPoints: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '奖励积分'
    },
    ip: {
      type: DataTypes.STRING(45),
      allowNull: true,
      comment: '分享IP'
    }
  }, {
    tableName: 'share_logs',
    comment: '分享记录表',
    indexes: [
      {
        fields: ['user_id']
      },
      {
        fields: ['content_id']
      },
      {
        fields: ['share_type']
      },
      {
        fields: ['created_at']
      }
    ]
  });

  return ShareLog;
};
