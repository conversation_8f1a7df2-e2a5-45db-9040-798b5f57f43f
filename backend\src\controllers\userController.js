const { User, PointLog, ContentUnlock, Favorite, InviteLog, SignLog } = require('../models');
const logger = require('../utils/logger');
const { BusinessError, ValidationError } = require('../middleware/errorHandler');

class UserController {
  // 获取用户信息
  async getProfile(ctx) {
    const userId = ctx.state.user.id;
    const user = await User.findByPk(userId);

    if (!user) {
      throw new BusinessError('用户不存在');
    }

    ctx.body = {
      code: 200,
      message: '获取成功',
      data: user.toJSON()
    };
  }

  // 更新用户信息
  async updateProfile(ctx) {
    const userId = ctx.state.user.id;
    const { nickname, avatar, gender, birthday } = ctx.request.body;

    const user = await User.findByPk(userId);
    if (!user) {
      throw new BusinessError('用户不存在');
    }

    // 更新字段
    if (nickname) user.nickname = nickname;
    if (avatar) user.avatar = avatar;
    if (gender !== undefined) user.gender = gender;
    if (birthday) user.birthday = birthday;

    await user.save();

    logger.business('更新用户信息', userId, { nickname, gender, birthday });

    ctx.body = {
      code: 200,
      message: '更新成功',
      data: user.toJSON()
    };
  }

  // 获取积分信息
  async getPoints(ctx) {
    const userId = ctx.state.user.id;
    const user = await User.findByPk(userId);

    if (!user) {
      throw new BusinessError('用户不存在');
    }

    // 获取今日积分获取统计
    const todayStats = await PointLog.getTodayGainStats(userId);

    ctx.body = {
      code: 200,
      message: '获取成功',
      data: {
        currentPoints: user.points,
        totalPoints: user.totalPoints,
        todayGain: todayStats.totalGain,
        todayStats: todayStats.stats
      }
    };
  }

  // 获取积分日志
  async getPointLogs(ctx) {
    const userId = ctx.state.user.id;
    const { page = 1, pageSize = 20, type } = ctx.query;

    const result = await PointLog.getUserLogs(userId, parseInt(page), parseInt(pageSize), type);

    ctx.body = {
      code: 200,
      message: '获取成功',
      data: {
        list: result.rows,
        total: result.count,
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      }
    };
  }

  // 获取解锁记录
  async getUnlocks(ctx) {
    const userId = ctx.state.user.id;
    const { page = 1, pageSize = 20 } = ctx.query;
    const offset = (parseInt(page) - 1) * parseInt(pageSize);

    const result = await ContentUnlock.findAndCountAll({
      where: { userId },
      include: [
        {
          model: require('../models').Content,
          as: 'content',
          attributes: ['id', 'title', 'coverImage', 'unlockType', 'unlockPrice']
        }
      ],
      order: [['createdAt', 'DESC']],
      limit: parseInt(pageSize),
      offset
    });

    ctx.body = {
      code: 200,
      message: '获取成功',
      data: {
        list: result.rows,
        total: result.count,
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      }
    };
  }

  // 获取收藏列表
  async getFavorites(ctx) {
    const userId = ctx.state.user.id;
    const { page = 1, pageSize = 20 } = ctx.query;
    const offset = (parseInt(page) - 1) * parseInt(pageSize);

    const result = await Favorite.findAndCountAll({
      where: { userId },
      include: [
        {
          model: require('../models').Content,
          as: 'content',
          attributes: ['id', 'title', 'summary', 'coverImage', 'unlockType', 'unlockPrice', 'viewCount', 'likeCount']
        }
      ],
      order: [['createdAt', 'DESC']],
      limit: parseInt(pageSize),
      offset
    });

    ctx.body = {
      code: 200,
      message: '获取成功',
      data: {
        list: result.rows,
        total: result.count,
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      }
    };
  }

  // 添加收藏
  async addFavorite(ctx) {
    const userId = ctx.state.user.id;
    const { contentId } = ctx.request.body;

    if (!contentId) {
      throw new ValidationError('内容ID不能为空');
    }

    // 检查内容是否存在
    const { Content } = require('../models');
    const content = await Content.findByPk(contentId);
    if (!content) {
      throw new BusinessError('内容不存在');
    }

    // 检查是否已收藏
    const existing = await Favorite.findOne({
      where: { userId, contentId }
    });

    if (existing) {
      throw new BusinessError('已收藏该内容');
    }

    // 添加收藏
    await Favorite.create({ userId, contentId });

    // 更新内容收藏数
    await content.incrementFavoriteCount();

    logger.business('添加收藏', userId, { contentId });

    ctx.body = {
      code: 200,
      message: '收藏成功'
    };
  }

  // 取消收藏
  async removeFavorite(ctx) {
    const userId = ctx.state.user.id;
    const { contentId } = ctx.params;

    const favorite = await Favorite.findOne({
      where: { userId, contentId }
    });

    if (!favorite) {
      throw new BusinessError('未收藏该内容');
    }

    await favorite.destroy();

    // 更新内容收藏数
    const { Content } = require('../models');
    const content = await Content.findByPk(contentId);
    if (content) {
      await content.decrementFavoriteCount();
    }

    logger.business('取消收藏', userId, { contentId });

    ctx.body = {
      code: 200,
      message: '取消收藏成功'
    };
  }

  // 获取邀请信息
  async getInviteInfo(ctx) {
    const userId = ctx.state.user.id;
    const user = await User.findByPk(userId);

    if (!user) {
      throw new BusinessError('用户不存在');
    }

    // 统计邀请数据
    const inviteCount = await InviteLog.count({
      where: { inviterId: userId }
    });

    const totalReward = await PointLog.sum('amount', {
      where: {
        userId,
        source: ['invite_register', 'invite_unlock']
      }
    }) || 0;

    ctx.body = {
      code: 200,
      message: '获取成功',
      data: {
        inviteCode: user.inviteCode,
        inviteCount,
        totalReward,
        shareUrl: `${ctx.origin}/invite/${user.inviteCode}`
      }
    };
  }

  // 获取邀请记录
  async getInviteLogs(ctx) {
    const userId = ctx.state.user.id;
    const { page = 1, pageSize = 20 } = ctx.query;
    const offset = (parseInt(page) - 1) * parseInt(pageSize);

    const result = await InviteLog.findAndCountAll({
      where: { inviterId: userId },
      include: [
        {
          model: User,
          as: 'invitee',
          attributes: ['id', 'nickname', 'avatar', 'createdAt']
        }
      ],
      order: [['createdAt', 'DESC']],
      limit: parseInt(pageSize),
      offset
    });

    ctx.body = {
      code: 200,
      message: '获取成功',
      data: {
        list: result.rows,
        total: result.count,
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      }
    };
  }

  // 获取签到信息
  async getSignInfo(ctx) {
    const userId = ctx.state.user.id;
    const today = new Date().toISOString().split('T')[0];

    // 检查今日是否已签到
    const todaySign = await SignLog.findOne({
      where: {
        userId,
        signDate: today
      }
    });

    // 获取连续签到天数
    let continuousDays = 0;
    if (todaySign) {
      continuousDays = todaySign.continuousDays;
    } else {
      // 查找昨天的签到记录
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      const yesterdayStr = yesterday.toISOString().split('T')[0];

      const lastSign = await SignLog.findOne({
        where: {
          userId,
          signDate: yesterdayStr
        }
      });

      if (lastSign) {
        continuousDays = lastSign.continuousDays;
      }
    }

    // 获取本月签到记录
    const thisMonth = new Date().toISOString().substr(0, 7);
    const monthlySignCount = await SignLog.count({
      where: {
        userId,
        signDate: {
          [require('sequelize').Op.like]: `${thisMonth}%`
        }
      }
    });

    ctx.body = {
      code: 200,
      message: '获取成功',
      data: {
        hasSignedToday: !!todaySign,
        continuousDays,
        monthlySignCount,
        todayReward: todaySign ? todaySign.points : null
      }
    };
  }

  // 获取VIP信息
  async getVipInfo(ctx) {
    const userId = ctx.state.user.id;
    const user = await User.findByPk(userId);

    if (!user) {
      throw new BusinessError('用户不存在');
    }

    const isVip = user.isVip();
    const remainingDays = user.getVipRemainingDays();

    ctx.body = {
      code: 200,
      message: '获取成功',
      data: {
        isVip,
        vipLevel: user.vipLevel,
        vipExpireTime: user.vipExpireTime,
        remainingDays
      }
    };
  }
}

module.exports = new UserController();
