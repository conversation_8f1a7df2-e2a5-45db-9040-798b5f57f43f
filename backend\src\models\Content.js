const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Content = sequelize.define('Content', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
      comment: '内容ID'
    },
    title: {
      type: DataTypes.STRING(200),
      allowNull: false,
      comment: '标题'
    },
    summary: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '摘要'
    },
    content: {
      type: DataTypes.TEXT('long'),
      allowNull: true,
      comment: '内容'
    },
    contentType: {
      type: DataTypes.ENUM('article', 'netdisk', 'video', 'document'),
      allowNull: false,
      defaultValue: 'article',
      comment: '内容类型：article文章 netdisk网盘 video视频 document文档'
    },
    coverImage: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '封面图'
    },
    netdiskUrl: {
      type: DataTypes.STRING(500),
      allowNull: true,
      comment: '网盘链接'
    },
    netdiskCode: {
      type: DataTypes.STRING(20),
      allowNull: true,
      comment: '网盘提取码'
    },
    videoUrl: {
      type: DataTypes.STRING(500),
      allowNull: true,
      comment: '视频链接'
    },
    categoryId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: '分类ID'
    },
    unlockType: {
      type: DataTypes.TINYINT,
      allowNull: false,
      defaultValue: 1,
      comment: '解锁方式：1积分 2VIP 3卡密 4免费'
    },
    unlockPrice: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '解锁价格（积分）'
    },
    viewCount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '浏览次数'
    },
    likeCount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '点赞次数'
    },
    unlockCount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '解锁次数'
    },
    shareCount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '分享次数'
    },
    commentCount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '评论次数'
    },
    favoriteCount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '收藏次数'
    },
    status: {
      type: DataTypes.TINYINT,
      allowNull: false,
      defaultValue: 1,
      comment: '状态：1正常 2下架 3审核中'
    },
    isRecommend: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: '是否推荐'
    },
    isTop: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: '是否置顶'
    },
    sortOrder: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '排序权重'
    },
    seoTitle: {
      type: DataTypes.STRING(200),
      allowNull: true,
      comment: 'SEO标题'
    },
    seoKeywords: {
      type: DataTypes.STRING(500),
      allowNull: true,
      comment: 'SEO关键词'
    },
    seoDescription: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'SEO描述'
    },
    createdBy: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '创建人'
    },
    publishTime: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '发布时间'
    }
  }, {
    tableName: 'contents',
    comment: '内容表',
    indexes: [
      {
        fields: ['category_id']
      },
      {
        fields: ['unlock_type']
      },
      {
        fields: ['status']
      },
      {
        fields: ['is_recommend', 'sort_order']
      },
      {
        fields: ['is_top', 'sort_order']
      },
      {
        fields: ['created_at']
      },
      {
        fields: ['publish_time']
      },
      {
        fields: ['view_count']
      },
      {
        fields: ['like_count']
      },
      {
        fields: ['unlock_count']
      }
    ]
  });

  // 实例方法
  Content.prototype.toJSON = function() {
    const values = { ...this.get() };
    
    // 根据内容类型处理显示字段
    if (values.contentType === 'netdisk') {
      // 网盘类型显示网盘信息
      values.netdisk = {
        url: values.netdiskUrl,
        code: values.netdiskCode
      };
    } else if (values.contentType === 'video') {
      // 视频类型显示视频信息
      values.video = {
        url: values.videoUrl
      };
    }
    
    return values;
  };

  // 检查用户是否可以访问内容
  Content.prototype.canAccess = function(user) {
    // 免费内容直接可访问
    if (this.unlockType === 4) {
      return true;
    }
    
    // 未登录用户不能访问付费内容
    if (!user) {
      return false;
    }
    
    // VIP内容检查VIP状态
    if (this.unlockType === 2) {
      return user.isVip();
    }
    
    // 积分内容检查积分余额
    if (this.unlockType === 1) {
      return user.points >= this.unlockPrice;
    }
    
    return false;
  };

  // 增加浏览次数
  Content.prototype.incrementViewCount = async function() {
    await this.increment('viewCount');
  };

  // 增加点赞次数
  Content.prototype.incrementLikeCount = async function() {
    await this.increment('likeCount');
  };

  // 减少点赞次数
  Content.prototype.decrementLikeCount = async function() {
    await this.decrement('likeCount');
  };

  // 增加解锁次数
  Content.prototype.incrementUnlockCount = async function() {
    await this.increment('unlockCount');
  };

  // 增加分享次数
  Content.prototype.incrementShareCount = async function() {
    await this.increment('shareCount');
  };

  // 增加评论次数
  Content.prototype.incrementCommentCount = async function() {
    await this.increment('commentCount');
  };

  // 减少评论次数
  Content.prototype.decrementCommentCount = async function() {
    await this.decrement('commentCount');
  };

  // 增加收藏次数
  Content.prototype.incrementFavoriteCount = async function() {
    await this.increment('favoriteCount');
  };

  // 减少收藏次数
  Content.prototype.decrementFavoriteCount = async function() {
    await this.decrement('favoriteCount');
  };

  // 计算热度分数
  Content.prototype.getHotScore = function() {
    const now = new Date();
    const createTime = new Date(this.createdAt);
    const hoursSinceCreated = (now - createTime) / (1000 * 60 * 60);
    
    // 热度计算公式：(点赞数 * 2 + 解锁数 * 3 + 分享数 * 4 + 评论数 * 1.5) / (时间衰减因子)
    const score = (
      this.likeCount * 2 + 
      this.unlockCount * 3 + 
      this.shareCount * 4 + 
      this.commentCount * 1.5
    ) / Math.pow(hoursSinceCreated + 2, 1.5);
    
    return Math.round(score * 100) / 100;
  };

  // 类方法
  // 获取热门内容
  Content.getHotContents = function(limit = 10) {
    return this.findAll({
      where: { status: 1 },
      order: [
        ['viewCount', 'DESC'],
        ['likeCount', 'DESC'],
        ['unlockCount', 'DESC']
      ],
      limit
    });
  };

  // 获取推荐内容
  Content.getRecommendContents = function(limit = 10) {
    return this.findAll({
      where: { 
        status: 1,
        isRecommend: true
      },
      order: [
        ['sortOrder', 'DESC'],
        ['createdAt', 'DESC']
      ],
      limit
    });
  };

  // 获取最新内容
  Content.getLatestContents = function(limit = 10) {
    return this.findAll({
      where: { status: 1 },
      order: [['createdAt', 'DESC']],
      limit
    });
  };

  // 根据分类获取内容
  Content.getByCategory = function(categoryId, page = 1, pageSize = 20) {
    const offset = (page - 1) * pageSize;
    return this.findAndCountAll({
      where: { 
        categoryId,
        status: 1
      },
      order: [
        ['isTop', 'DESC'],
        ['sortOrder', 'DESC'],
        ['createdAt', 'DESC']
      ],
      limit: pageSize,
      offset
    });
  };

  // 搜索内容
  Content.search = function(keyword, page = 1, pageSize = 20) {
    const offset = (page - 1) * pageSize;
    const { Op } = require('sequelize');
    
    return this.findAndCountAll({
      where: {
        status: 1,
        [Op.or]: [
          { title: { [Op.like]: `%${keyword}%` } },
          { summary: { [Op.like]: `%${keyword}%` } },
          { seoKeywords: { [Op.like]: `%${keyword}%` } }
        ]
      },
      order: [
        ['isTop', 'DESC'],
        ['viewCount', 'DESC'],
        ['createdAt', 'DESC']
      ],
      limit: pageSize,
      offset
    });
  };

  return Content;
};
