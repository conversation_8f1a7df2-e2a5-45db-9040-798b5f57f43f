const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const ContentUnlock = sequelize.define('ContentUnlock', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
      comment: '解锁记录ID'
    },
    userId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '用户ID'
    },
    contentId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '内容ID'
    },
    unlockType: {
      type: DataTypes.TINYINT,
      allowNull: false,
      comment: '解锁方式：1积分 2VIP 3卡密'
    },
    unlockPrice: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '解锁消耗积分'
    },
    cardId: {
      type: DataTypes.BIGINT,
      allowNull: true,
      comment: '卡密ID（卡密解锁时记录）'
    },
    ip: {
      type: DataTypes.STRING(45),
      allowNull: true,
      comment: '解锁IP'
    }
  }, {
    tableName: 'content_unlocks',
    comment: '内容解锁记录表',
    indexes: [
      {
        unique: true,
        fields: ['user_id', 'content_id']
      },
      {
        fields: ['content_id']
      },
      {
        fields: ['unlock_type']
      },
      {
        fields: ['created_at']
      }
    ]
  });

  return ContentUnlock;
};
