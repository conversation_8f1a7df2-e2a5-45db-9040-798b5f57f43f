const { Sequelize } = require('sequelize');
const config = require('../config/config');
const logger = require('../utils/logger');

// 创建Sequelize实例
const sequelize = new Sequelize(
  config.database.database,
  config.database.username,
  config.database.password,
  {
    host: config.database.host,
    port: config.database.port,
    dialect: config.database.dialect,
    timezone: config.database.timezone,
    pool: config.database.pool,
    logging: config.database.logging,
    define: {
      // 全局模型配置
      timestamps: true, // 自动添加createdAt和updatedAt
      underscored: false, // 使用驼峰命名
      freezeTableName: true, // 禁用表名复数化
      charset: 'utf8mb4',
      collate: 'utf8mb4_unicode_ci'
    }
  }
);

// 导入模型
const User = require('./User')(sequelize);
const Content = require('./Content')(sequelize);
const Category = require('./Category')(sequelize);
const Tag = require('./Tag')(sequelize);
const ContentTag = require('./ContentTag')(sequelize);
const ContentUnlock = require('./ContentUnlock')(sequelize);
const PointLog = require('./PointLog')(sequelize);
const SignLog = require('./SignLog')(sequelize);
const AdLog = require('./AdLog')(sequelize);
const Card = require('./Card')(sequelize);
const CardBatch = require('./CardBatch')(sequelize);
const InviteLog = require('./InviteLog')(sequelize);
const ShareLog = require('./ShareLog')(sequelize);
const Comment = require('./Comment')(sequelize);
const CommentLike = require('./CommentLike')(sequelize);
const Favorite = require('./Favorite')(sequelize);
const ContentLike = require('./ContentLike')(sequelize);
const Admin = require('./Admin')(sequelize);
const AdminRole = require('./AdminRole')(sequelize);
const AdminLog = require('./AdminLog')(sequelize);
const UiConfig = require('./UiConfig')(sequelize);
const PointConfig = require('./PointConfig')(sequelize);

// 定义模型关联关系
const setupAssociations = () => {
  // 用户相关关联
  User.hasMany(Content, { foreignKey: 'createdBy', as: 'contents' });
  User.hasMany(ContentUnlock, { foreignKey: 'userId', as: 'unlocks' });
  User.hasMany(PointLog, { foreignKey: 'userId', as: 'pointLogs' });
  User.hasMany(SignLog, { foreignKey: 'userId', as: 'signLogs' });
  User.hasMany(AdLog, { foreignKey: 'userId', as: 'adLogs' });
  User.hasMany(InviteLog, { foreignKey: 'inviterId', as: 'inviteLogs' });
  User.hasMany(InviteLog, { foreignKey: 'inviteeId', as: 'invitedLogs' });
  User.hasMany(ShareLog, { foreignKey: 'userId', as: 'shareLogs' });
  User.hasMany(Comment, { foreignKey: 'userId', as: 'comments' });
  User.hasMany(CommentLike, { foreignKey: 'userId', as: 'commentLikes' });
  User.hasMany(Favorite, { foreignKey: 'userId', as: 'favorites' });
  User.hasMany(ContentLike, { foreignKey: 'userId', as: 'contentLikes' });
  User.belongsTo(User, { foreignKey: 'inviterId', as: 'inviter' });

  // 内容相关关联
  Content.belongsTo(User, { foreignKey: 'createdBy', as: 'creator' });
  Content.belongsTo(Category, { foreignKey: 'categoryId', as: 'category' });
  Content.hasMany(ContentUnlock, { foreignKey: 'contentId', as: 'unlocks' });
  Content.hasMany(ShareLog, { foreignKey: 'contentId', as: 'shareLogs' });
  Content.hasMany(Comment, { foreignKey: 'contentId', as: 'comments' });
  Content.hasMany(Favorite, { foreignKey: 'contentId', as: 'favorites' });
  Content.hasMany(ContentLike, { foreignKey: 'contentId', as: 'likes' });
  Content.belongsToMany(Tag, { 
    through: ContentTag, 
    foreignKey: 'contentId', 
    otherKey: 'tagId',
    as: 'tags' 
  });

  // 分类关联
  Category.hasMany(Content, { foreignKey: 'categoryId', as: 'contents' });
  Category.belongsTo(Category, { foreignKey: 'parentId', as: 'parent' });
  Category.hasMany(Category, { foreignKey: 'parentId', as: 'children' });

  // 标签关联
  Tag.belongsToMany(Content, { 
    through: ContentTag, 
    foreignKey: 'tagId', 
    otherKey: 'contentId',
    as: 'contents' 
  });

  // 内容解锁关联
  ContentUnlock.belongsTo(User, { foreignKey: 'userId', as: 'user' });
  ContentUnlock.belongsTo(Content, { foreignKey: 'contentId', as: 'content' });

  // 积分日志关联
  PointLog.belongsTo(User, { foreignKey: 'userId', as: 'user' });

  // 签到日志关联
  SignLog.belongsTo(User, { foreignKey: 'userId', as: 'user' });

  // 广告日志关联
  AdLog.belongsTo(User, { foreignKey: 'userId', as: 'user' });

  // 卡密关联
  Card.belongsTo(CardBatch, { foreignKey: 'batchId', as: 'batch' });
  Card.belongsTo(User, { foreignKey: 'usedBy', as: 'user' });
  CardBatch.hasMany(Card, { foreignKey: 'batchId', as: 'cards' });
  CardBatch.belongsTo(Admin, { foreignKey: 'createdBy', as: 'creator' });

  // 邀请日志关联
  InviteLog.belongsTo(User, { foreignKey: 'inviterId', as: 'inviter' });
  InviteLog.belongsTo(User, { foreignKey: 'inviteeId', as: 'invitee' });

  // 分享日志关联
  ShareLog.belongsTo(User, { foreignKey: 'userId', as: 'user' });
  ShareLog.belongsTo(Content, { foreignKey: 'contentId', as: 'content' });

  // 评论关联
  Comment.belongsTo(User, { foreignKey: 'userId', as: 'user' });
  Comment.belongsTo(Content, { foreignKey: 'contentId', as: 'content' });
  Comment.belongsTo(Comment, { foreignKey: 'parentId', as: 'parent' });
  Comment.hasMany(Comment, { foreignKey: 'parentId', as: 'replies' });
  Comment.hasMany(CommentLike, { foreignKey: 'commentId', as: 'likes' });

  // 评论点赞关联
  CommentLike.belongsTo(User, { foreignKey: 'userId', as: 'user' });
  CommentLike.belongsTo(Comment, { foreignKey: 'commentId', as: 'comment' });

  // 收藏关联
  Favorite.belongsTo(User, { foreignKey: 'userId', as: 'user' });
  Favorite.belongsTo(Content, { foreignKey: 'contentId', as: 'content' });

  // 内容点赞关联
  ContentLike.belongsTo(User, { foreignKey: 'userId', as: 'user' });
  ContentLike.belongsTo(Content, { foreignKey: 'contentId', as: 'content' });

  // 管理员关联
  Admin.belongsTo(AdminRole, { foreignKey: 'roleId', as: 'role' });
  Admin.hasMany(AdminLog, { foreignKey: 'adminId', as: 'logs' });
  AdminRole.hasMany(Admin, { foreignKey: 'roleId', as: 'admins' });

  // 管理员日志关联
  AdminLog.belongsTo(Admin, { foreignKey: 'adminId', as: 'admin' });

  // 积分配置关联
  PointConfig.belongsTo(Admin, { foreignKey: 'createdBy', as: 'creator' });
  PointConfig.belongsTo(Admin, { foreignKey: 'updatedBy', as: 'updater' });

  // UI配置关联
  UiConfig.belongsTo(Admin, { foreignKey: 'updatedBy', as: 'updater' });
};

// 设置关联关系
setupAssociations();

// 数据库连接测试
const testConnection = async () => {
  try {
    await sequelize.authenticate();
    logger.info('数据库连接成功');
  } catch (error) {
    logger.error('数据库连接失败', error);
    throw error;
  }
};

// 同步数据库（开发环境）
const syncDatabase = async (force = false) => {
  try {
    await sequelize.sync({ force });
    logger.info(`数据库同步完成 ${force ? '(强制重建)' : ''}`);
  } catch (error) {
    logger.error('数据库同步失败', error);
    throw error;
  }
};

module.exports = {
  sequelize,
  testConnection,
  syncDatabase,
  // 导出所有模型
  User,
  Content,
  Category,
  Tag,
  ContentTag,
  ContentUnlock,
  PointLog,
  SignLog,
  AdLog,
  Card,
  CardBatch,
  InviteLog,
  ShareLog,
  Comment,
  CommentLike,
  Favorite,
  ContentLike,
  Admin,
  AdminRole,
  AdminLog,
  UiConfig,
  PointConfig
};
