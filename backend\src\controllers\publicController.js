const { Content, Category, Tag, User, UiConfig } = require('../models');

class PublicController {
  // 获取分类列表
  async getCategories(ctx) {
    const categories = await Category.findAll({
      where: { status: 1 },
      order: [['sortOrder', 'DESC'], ['createdAt', 'ASC']],
      attributes: ['id', 'name', 'slug', 'description', 'icon', 'contentCount']
    });

    ctx.body = {
      code: 200,
      message: '获取成功',
      data: categories
    };
  }

  // 获取标签列表
  async getTags(ctx) {
    const { limit = 50 } = ctx.query;

    const tags = await Tag.findAll({
      order: [['contentCount', 'DESC'], ['sortOrder', 'DESC']],
      limit: parseInt(limit),
      attributes: ['id', 'name', 'color', 'contentCount']
    });

    ctx.body = {
      code: 200,
      message: '获取成功',
      data: tags
    };
  }

  // 获取热门内容
  async getHotContents(ctx) {
    const { limit = 10 } = ctx.query;

    const contents = await Content.getHotContents(parseInt(limit));

    ctx.body = {
      code: 200,
      message: '获取成功',
      data: contents
    };
  }

  // 获取推荐内容
  async getRecommendContents(ctx) {
    const { limit = 10 } = ctx.query;

    const contents = await Content.getRecommendContents(parseInt(limit));

    ctx.body = {
      code: 200,
      message: '获取成功',
      data: contents
    };
  }

  // 获取最新内容
  async getLatestContents(ctx) {
    const { limit = 10 } = ctx.query;

    const contents = await Content.getLatestContents(parseInt(limit));

    ctx.body = {
      code: 200,
      message: '获取成功',
      data: contents
    };
  }

  // 获取首页数据
  async getHomeData(ctx) {
    // 并行获取各种数据
    const [
      banners,
      hotContents,
      recommendContents,
      latestContents,
      categories
    ] = await Promise.all([
      this.getBanners(),
      Content.getHotContents(6),
      Content.getRecommendContents(8),
      Content.getLatestContents(10),
      Category.findAll({
        where: { status: 1 },
        order: [['sortOrder', 'DESC']],
        limit: 8,
        attributes: ['id', 'name', 'slug', 'icon', 'contentCount']
      })
    ]);

    ctx.body = {
      code: 200,
      message: '获取成功',
      data: {
        banners,
        hotContents,
        recommendContents,
        latestContents,
        categories
      }
    };
  }

  // 获取系统配置
  async getSystemConfig(ctx) {
    const configs = await UiConfig.findAll({
      attributes: ['key', 'value', 'description']
    });

    const configMap = {};
    configs.forEach(config => {
      try {
        // 尝试解析JSON
        configMap[config.key] = JSON.parse(config.value);
      } catch (error) {
        // 如果不是JSON，直接使用字符串值
        configMap[config.key] = config.value;
      }
    });

    ctx.body = {
      code: 200,
      message: '获取成功',
      data: configMap
    };
  }

  // 获取公开统计信息
  async getPublicStats(ctx) {
    const [
      totalContents,
      totalUsers,
      totalUnlocks,
      todayContents
    ] = await Promise.all([
      Content.count({ where: { status: 1 } }),
      User.count({ where: { status: 1 } }),
      require('../models').ContentUnlock.count(),
      Content.count({
        where: {
          status: 1,
          createdAt: {
            [require('sequelize').Op.gte]: new Date().setHours(0, 0, 0, 0)
          }
        }
      })
    ]);

    ctx.body = {
      code: 200,
      message: '获取成功',
      data: {
        totalContents,
        totalUsers,
        totalUnlocks,
        todayContents
      }
    };
  }

  // 获取轮播图配置
  async getBanners() {
    const bannerConfig = await UiConfig.findOne({
      where: { key: 'home_banner' }
    });

    if (bannerConfig) {
      try {
        return JSON.parse(bannerConfig.value);
      } catch (error) {
        return [];
      }
    }

    // 默认轮播图
    return [
      {
        title: '欢迎来到知识付费平台',
        image: '/images/banner1.jpg',
        link: ''
      }
    ];
  }
}

module.exports = new PublicController();
