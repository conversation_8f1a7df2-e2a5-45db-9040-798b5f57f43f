const Router = require('koa-router');
const contentController = require('../controllers/contentController');

const router = new Router();

// 获取内容列表
router.get('/', contentController.getList);

// 搜索内容
router.get('/search', contentController.search);

// 获取内容详情
router.get('/:id', contentController.getDetail);

// 解锁内容
router.post('/:id/unlock', contentController.unlock);

// 点赞内容
router.post('/:id/like', contentController.like);

// 取消点赞
router.delete('/:id/like', contentController.unlike);

// 收藏内容
router.post('/:id/favorite', contentController.favorite);

// 取消收藏
router.delete('/:id/favorite', contentController.unfavorite);

// 分享内容
router.post('/:id/share', contentController.share);

// 获取评论列表
router.get('/:id/comments', contentController.getComments);

// 发表评论
router.post('/:id/comments', contentController.addComment);

// 点赞评论
router.post('/comments/:commentId/like', contentController.likeComment);

// 取消评论点赞
router.delete('/comments/:commentId/like', contentController.unlikeComment);

// 删除评论
router.delete('/comments/:commentId', contentController.deleteComment);

// 获取相关推荐
router.get('/:id/related', contentController.getRelated);

module.exports = router;
