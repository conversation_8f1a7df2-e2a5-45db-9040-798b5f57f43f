const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const AdminLog = sequelize.define('AdminLog', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
      comment: '日志ID'
    },
    adminId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '管理员ID'
    },
    action: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: '操作动作'
    },
    target: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: '操作目标'
    },
    targetId: {
      type: DataTypes.BIGINT,
      allowNull: true,
      comment: '目标ID'
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '操作描述'
    },
    ip: {
      type: DataTypes.STRING(45),
      allowNull: true,
      comment: '操作IP'
    },
    userAgent: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '用户代理'
    }
  }, {
    tableName: 'admin_logs',
    comment: '管理员操作日志表',
    indexes: [
      {
        fields: ['admin_id']
      },
      {
        fields: ['action']
      },
      {
        fields: ['target']
      },
      {
        fields: ['created_at']
      }
    ]
  });

  return AdminLog;
};
