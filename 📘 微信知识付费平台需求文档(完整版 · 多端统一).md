# 📘 微信知识付费平台需求文档（完整版 · 多端统一）

## 一、项目概览

### 1.1 项目名称
**知识付费平台（微信小程序 + 网页端）**

### 1.2 项目愿景
打造一个覆盖考试与自学用户、支持微信与网页访问、具有积分激励与传播裂变能力的内容分发平台。

### 1.3 项目目标
- **内容价值**：提供高质量的文章/网盘等学习资源
- **商业变现**：建立积分/VIP/卡密解锁机制，促进内容变现
- **用户增长**：构建可持续增长的用户邀请、激励和分享体系
- **多端统一**：支持微信小程序与网页（H5）双端访问，账号数据打通
- **用户体验**：提供流畅、便捷的学习和内容消费体验

### 1.4 目标用户

| 用户类型 | 描述 | 核心需求 |
|---------|------|---------|
| 应试类用户 | 考研、考证、公务员考试人群 | 高质量备考资料、真题解析、学习计划 |
| 自学用户 | 有技能提升或知识兴趣的个人 | 系统化课程、实用技能、兴趣拓展 |
| 社群分发者 | 有资源或流量，愿意推广分享的用户 | 分享激励、佣金收益、推广工具 |

### 1.5 核心竞争优势
- **多端统一**：微信生态 + 网页端无缝切换
- **激励体系**：积分、VIP、邀请多重激励机制
- **内容丰富**：文章 + 网盘资源多样化内容形式
- **社交裂变**：基于微信生态的天然传播优势
---

## 二、平台架构

### 2.1 技术架构总览

| 层级 | 技术/服务 | 说明 |
|------|----------|------|
| **前端** | 微信小程序原生 + Vue3 网页端 | 小程序使用原生开发，网页端使用Vue3 + TypeScript |
| **后端** | Node.js + Koa REST API | RESTful API设计，支持中间件扩展 |
| **数据库** | MySQL（主数据库） | 关系型数据库，支持事务和复杂查询 |
| **缓存** | Redis（积分缓存、配置） | 热点数据缓存，提升响应速度 |
| **存储** | OSS / COS / MinIO（文章封面等） | 静态资源存储，支持CDN加速 |
| **鉴权** | JWT + 微信 openid/手机号登录 | 多端统一身份认证 |
| **管理后台** | Vue3 + Naive UI 或 ElementPlus | 现代化管理界面 |
| **运维** | Docker、Nginx、Vercel、自托管部署 | 容器化部署，支持多种部署方式 |

### 2.2 系统架构图
```
┌─────────────────┐    ┌─────────────────┐
│   微信小程序     │    │    网页端       │
│   (原生开发)     │    │   (Vue3+TS)     │
└─────────────────┘    └─────────────────┘
         │                       │
         └───────────┬───────────┘
                     │
         ┌─────────────────┐
         │   API Gateway   │
         │   (Nginx/Koa)   │
         └─────────────────┘
                     │
         ┌─────────────────┐
         │   业务服务层     │
         │  (Node.js+Koa)  │
         └─────────────────┘
                     │
    ┌────────────────┼────────────────┐
    │                │                │
┌───────┐      ┌───────┐      ┌───────┐
│ MySQL │      │ Redis │      │  OSS  │
│(主库) │      │(缓存) │      │(存储) │
└───────┘      └───────┘      └───────┘
```

### 2.3 部署架构
- **开发环境**：本地Docker容器 + 热重载
- **测试环境**：云服务器 + CI/CD自动部署
- **生产环境**：负载均衡 + 多实例部署 + 数据库主从
---

## 三、用户系统设计

### 3.1 用户身份体系

| 平台 | 登录方式 | 用户标识 | 特点 |
|------|----------|----------|------|
| 微信小程序 | 微信 openid 授权 | openid | 无感登录，用户体验最佳 |
| 微信网页 | 微信 OAuth 网页授权 | openid | 与小程序账号打通 |
| PC 网页 | 手机验证码登录 | mobile + JWT | 支持非微信环境访问 |
| 后台管理员 | 账号+密码+角色权限 | admin_user | 多角色权限管理 |

### 3.2 用户信息字段

#### 基础信息
- **昵称**：用户显示名称，支持修改
- **头像**：用户头像URL，微信授权获取或自定义上传
- **openid**：微信唯一标识，用于账号关联
- **手机号**：用户手机号，用于找回账号和重要通知

#### 业务信息
- **当前积分**：用户可用积分余额
- **VIP状态与有效期**：VIP等级、开通时间、到期时间
- **邀请人信息**：邀请关系链，用于分佣计算
- **注册时间**：用户注册时间戳
- **封禁状态**：账号状态（正常/封禁/限制）

#### 统计信息
- **累计消费积分**：历史总消费积分
- **累计邀请人数**：成功邀请的用户数量
- **最后登录时间**：用户活跃度统计
- **内容解锁数量**：已解锁的内容数量

### 3.3 账号关联策略
- **微信生态内**：通过openid实现小程序与网页端账号统一
- **跨平台关联**：通过手机号绑定实现PC端与微信端账号关联
- **数据同步**：积分、VIP状态、解锁记录等核心数据实时同步
---

## 四、产品功能模块

### 4.1 内容模块

#### 内容形式
- **文章内容**：富文本编辑器支持，包含图片、视频、代码块等
- **网盘资源**：百度网盘、阿里云盘等链接 + 提取码
- **视频课程**：在线视频播放或外链跳转
- **文档资料**：PDF、Word等文档在线预览

#### 解锁方式
| 解锁方式 | 说明 | 适用场景 |
|---------|------|---------|
| **积分解锁** | 消耗用户积分 | 普通用户获取内容的主要方式 |
| **VIP解锁** | 仅限VIP用户免费访问 | VIP专享内容，提升VIP价值 |
| **卡密解锁** | 使用卡密一次性解锁 | 推广活动、礼品赠送 |
| **免费内容** | 无需解锁直接访问 | 引流内容，吸引新用户 |

#### 内容管理功能
- **分类标签**：支持多级分类、多标签关联
- **排序方式**：最新发布、热门程度、积分价格、收藏数量
- **封面图片**：支持上传自定义封面，自动生成缩略图
- **内容状态**：草稿、已发布、已下架、推荐等状态管理
- **SEO优化**：标题、描述、关键词设置

### 4.2 解锁系统

#### 解锁逻辑
```
用户请求内容 → 检查解锁状态 → 判断解锁方式 → 执行解锁操作 → 记录解锁日志
```

#### 解锁规则
- **积分解锁**：扣除对应积分，积分不足时提示获取方式
- **VIP解锁**：验证VIP有效期，过期用户引导续费
- **卡密解锁**：验证卡密有效性，使用后自动失效
- **重复解锁**：已解锁内容永久有效，无需重复付费

#### 解锁记录
- 记录用户ID、内容ID、解锁方式、解锁时间
- 支持解锁历史查询和数据统计
- 异常解锁监控和风控处理

### 4.3 积分系统

#### 积分获取方式

| 获取方式 | 积分数量（默认值） | 限制条件 | 说明 | 后台可配置 |
|---------|------------------|---------|------|-----------|
| **每日签到** | 5积分（基础）<br>10积分（连续7天）<br>20积分（连续30天） | 每日1次 | 连续签到有额外奖励 | ✅ 可配置基础积分和连续奖励 |
| **观看广告** | 3积分/次 | 每日5次 | 激励视频广告 | ✅ 可配置单次积分和每日次数 |
| **邀请注册** | 20积分 | 无限制 | 被邀请人成功注册 | ✅ 可配置奖励积分数量 |
| **邀请解锁** | 解锁积分的20% | 无限制 | 被邀请人解锁内容 | ✅ 可配置分成比例 |
| **分享传播** | 2积分/次 | 每日10次 | 分享内容被点击 | ✅ 可配置单次积分和每日次数 |
| **卡密兑换** | 不定 | 卡密有效 | 积分卡兑换 | ✅ 卡密生成时配置 |
| **内容创作** | 100积分 | 审核通过 | 用户投稿优质内容 | ✅ 可配置奖励积分数量 |
| **完善资料** | 10积分 | 仅一次 | 完善个人资料信息 | ✅ 可配置奖励积分数量 |
| **首次解锁** | 5积分 | 仅一次 | 用户首次解锁内容奖励 | ✅ 可配置奖励积分数量 |

#### 积分消耗记录
- **解锁内容**：根据内容价值设定不同积分价格
- **兑换卡密**：积分换取VIP卡或其他权益
- **商城兑换**：积分商城兑换实物或虚拟商品

#### 积分系统限制
- **防刷机制**：IP限制、设备指纹、行为分析
- **每日上限**：各种获取方式设置每日上限（后台可配置）
- **积分有效期**：积分设置有效期，促进消费（默认365天，后台可配置）
- **完整日志**：所有积分变动记录，支持审计

#### 积分配置管理
- **全局配置**：在管理后台统一配置所有积分获取规则
- **实时生效**：配置修改后实时生效，无需重启系统
- **历史记录**：保留配置修改历史，支持回滚
- **A/B测试**：支持不同用户群体使用不同积分规则
### 4.4 卡密系统

#### 卡密类型
| 卡密类型 | 功能说明 | 使用场景 |
|---------|---------|---------|
| **积分卡** | 兑换指定数量积分 | 推广活动、用户奖励 |
| **VIP卡** | 开通VIP会员权限 | 会员推广、礼品赠送 |
| **内容卡** | 解锁特定内容 | 内容推广、限时活动 |
| **通用卡** | 多种权益组合 | 节日活动、大型推广 |

#### 卡密管理功能
- **批量生成**：支持批量生成指定数量和类型的卡密
- **批次管理**：卡密按批次管理，便于追踪和统计
- **导出功能**：支持Excel、CSV格式导出卡密列表
- **状态管理**：未使用、已使用、已过期、已作废状态
- **使用记录**：记录使用用户、使用时间、使用IP等信息

#### 卡密安全机制
- **唯一性校验**：确保卡密唯一性，防止重复生成
- **有效期控制**：支持设置卡密有效期
- **使用限制**：单个卡密只能使用一次
- **风控监控**：异常使用行为监控和预警

### 4.5 评论互动模块

#### 评论功能
- **发布评论**：支持文字评论，表情包，@用户功能
- **回复评论**：支持多级回复，最多3级嵌套
- **评论排序**：按时间、热度、点赞数排序
- **分页加载**：支持分页和无限滚动加载

#### 互动功能
- **点赞系统**：内容点赞、评论点赞，防重复点赞
- **收藏功能**：收藏喜欢的内容，个人收藏夹管理
- **分享功能**：分享到微信、朋友圈、QQ等社交平台
- **举报功能**：不良内容举报，社区自治

#### 内容审核
- **敏感词过滤**：自动过滤敏感词汇
- **人工审核**：可疑内容人工审核机制
- **用户举报**：用户举报处理流程
- **违规处理**：警告、禁言、封号等处理措施

### 4.6 分享与邀请模块

#### 邀请机制
- **专属邀请码**：每个用户拥有唯一邀请码
- **邀请链接**：生成专属邀请链接，支持参数追踪
- **二维码分享**：自动生成邀请二维码，便于分享
- **邀请关系**：建立邀请关系链，支持多级分佣

#### 奖励机制
| 邀请行为 | 邀请人奖励 | 被邀请人奖励 | 说明 |
|---------|-----------|-------------|------|
| **注册成功** | 20积分 | 10积分 | 基础注册奖励 |
| **首次解锁** | 解锁积分的30% | 5积分 | 促进首次消费 |
| **成为VIP** | 50积分 | 20积分 | VIP推广奖励 |
| **持续活跃** | 每月10积分 | - | 长期活跃奖励 |

#### 分享追踪
- **分享统计**：分享次数、点击次数、转化率统计
- **渠道分析**：不同分享渠道效果分析
- **用户画像**：分享用户和被分享用户画像分析
- **奖励发放**：自动计算和发放分享奖励

### 4.7 个性化推荐模块

#### 推荐算法
```
推荐分数 = 内容质量分 × 用户兴趣匹配度 × 时效性权重 × 社交影响力
```

#### 行为权重设置
| 用户行为 | 权重系数 | 说明 |
|---------|---------|------|
| **浏览点击** | 1.0 | 基础兴趣指标 |
| **收藏内容** | 3.0 | 强兴趣指标 |
| **解锁内容** | 5.0 | 付费意愿指标 |
| **分享传播** | 4.0 | 认可度指标 |
| **评论互动** | 2.0 | 参与度指标 |

#### 推荐策略
- **标签相似度**：基于内容标签计算相似度
- **协同过滤**：基于用户行为的协同过滤推荐
- **热度推荐**：结合内容热度和时效性
- **冷启动处理**：新用户和新内容的推荐策略
- **多样性保证**：避免推荐结果过于单一

#### 推荐场景
- **首页推荐**：个性化首页内容推荐
- **相关推荐**：内容详情页相关内容推荐
- **分类推荐**：分类页面个性化排序
- **搜索推荐**：搜索结果个性化排序
---

## 五、页面设计规划（小程序 & 网页端统一）

### 5.1 公共页面模块

| 页面 | 功能说明 | 核心组件 | 特殊要求 |
|------|---------|---------|---------|
| **首页** | 推荐内容、公告栏、热门资源 | 轮播图、内容卡片、分类导航 | 个性化推荐、下拉刷新 |
| **分类页** | 标签导航、内容筛选排序 | 分类树、筛选器、排序组件 | 无限滚动、搜索功能 |
| **内容详情页** | 解锁状态判断、正文展示、评论 | 解锁组件、富文本渲染、评论列表 | 防盗链、分享统计 |
| **登录/注册页** | openid登录/手机号登录 | 授权按钮、验证码输入 | 多端适配、安全验证 |
| **我的页面** | 个人资料、积分、VIP、收藏 | 用户信息卡、功能菜单 | 数据实时更新 |
| **邀请页面** | 邀请码、二维码、邀请记录 | 二维码生成、分享组件 | 分享追踪、奖励展示 |
| **卡密兑换页** | 输入卡密、兑换提示与结果 | 输入框、结果反馈 | 防重复提交、错误处理 |
| **积分获取页** | 签到、广告、邀请说明入口 | 签到日历、广告组件 | 状态同步、奖励动画 |
| **搜索页面** | 内容搜索、历史记录、热门搜索 | 搜索框、结果列表、标签云 | 搜索建议、结果高亮 |
| **收藏页面** | 收藏内容管理、分类整理 | 内容列表、分类标签 | 批量操作、同步状态 |
| **积分配置页（管理后台）** | 积分规则配置、实时调整 | 配置表单、预览组件 | 实时生效、历史记录 |

### 5.2 页面交互设计

#### 导航结构
```
首页 (推荐内容)
├── 分类页 (内容分类)
├── 搜索页 (内容搜索)
├── 我的页面
│   ├── 个人资料
│   ├── 积分中心
│   ├── VIP中心
│   ├── 我的收藏
│   ├── 解锁记录
│   ├── 邀请好友
│   └── 设置中心
└── 内容详情页
    ├── 内容展示
    ├── 评论区
    └── 相关推荐
```

#### 响应式设计
- **小程序端**：原生组件，遵循微信设计规范
- **移动网页端**：响应式布局，适配不同屏幕尺寸
- **PC网页端**：桌面端优化，支持键盘快捷键

#### 用户体验优化
- **加载优化**：骨架屏、懒加载、预加载
- **交互反馈**：加载状态、操作反馈、错误提示
- **无障碍访问**：语义化标签、键盘导航、屏幕阅读器支持
---

## 六、后端系统设计（Node.js + Koa）

### 6.1 模块概览

| 模块 | 功能说明 | 核心接口 | 依赖关系 |
|------|---------|---------|---------|
| **用户模块** | openid/手机号登录，邀请关系绑定，VIP判断 | 登录、注册、用户信息、VIP管理 | 基础模块 |
| **内容模块** | 内容增删查改、分类管理、解锁判断 | 内容CRUD、分类管理、解锁验证 | 依赖用户模块 |
| **积分模块** | 获取与消耗逻辑，日志记录 | 积分变动、日志查询、规则配置 | 依赖用户模块 |
| **卡密模块** | 卡密生成、作废、兑换逻辑 | 卡密CRUD、批量操作、兑换接口 | 依赖积分模块 |
| **评论模块** | 发布、点赞、分页加载 | 评论CRUD、点赞、审核 | 依赖用户、内容模块 |
| **分享模块** | 分享链接、记录、积分奖励发放 | 分享统计、奖励计算、链接生成 | 依赖用户、积分模块 |
| **推荐模块** | 推荐接口、标签权重分配 | 个性化推荐、热门推荐 | 依赖用户、内容模块 |
| **配置模块** | 控制首页模块显示、积分规则配置 | 配置管理、缓存更新 | 独立模块 |
| **鉴权模块** | JWT鉴权，权限校验中间件 | 登录验证、权限检查 | 基础中间件 |
| **管理员模块** | 登录、角色权限（RBAC）、操作日志 | 管理员CRUD、权限管理、日志 | 独立权限体系 |

### 6.2 API设计规范

#### RESTful API设计
```
# 内容相关
GET    /api/v1/contents          # 获取内容列表
POST   /api/v1/contents          # 创建内容
GET    /api/v1/contents/:id      # 获取单个内容
PUT    /api/v1/contents/:id      # 更新内容
DELETE /api/v1/contents/:id      # 删除内容

# 积分配置相关
GET    /api/v1/admin/point-config           # 获取所有积分配置
GET    /api/v1/admin/point-config/:key      # 获取单个配置
PUT    /api/v1/admin/point-config/:key      # 更新单个配置
POST   /api/v1/admin/point-config/batch     # 批量更新配置
GET    /api/v1/admin/point-config/history   # 获取配置历史
POST   /api/v1/admin/point-config/rollback  # 回滚配置

# 用户端积分配置查询（缓存优化）
GET    /api/v1/point-rules                  # 获取当前生效的积分规则
```

#### 统一响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": 1640995200000,
  "requestId": "uuid-string"
}
```

#### 错误码规范
| 错误码 | 说明 | 示例场景 |
|-------|------|---------|
| 200 | 成功 | 正常请求 |
| 400 | 请求参数错误 | 参数缺失或格式错误 |
| 401 | 未授权 | 未登录或token过期 |
| 403 | 权限不足 | 无权限访问资源 |
| 404 | 资源不存在 | 内容不存在 |
| 429 | 请求过于频繁 | 触发限流 |
| 500 | 服务器内部错误 | 系统异常 |

### 6.3 数据库设计原则

#### 设计原则
- **规范化**：遵循第三范式，减少数据冗余
- **性能优化**：合理使用索引，避免N+1查询
- **扩展性**：预留扩展字段，支持业务发展
- **一致性**：使用事务保证数据一致性

#### 索引策略
- **主键索引**：所有表必须有主键
- **唯一索引**：openid、手机号等唯一字段
- **复合索引**：多字段查询场景
- **覆盖索引**：减少回表查询
---

## 七、管理后台功能

### 7.1 核心功能模块

| 功能模块 | 子功能 | 权限要求 | 操作日志 |
|---------|-------|---------|---------|
| **内容管理** | 发布、编辑、上下架、分类管理、批量导入导出 | 内容管理员+ | 记录所有内容操作 |
| **用户管理** | 用户查询、积分调整、VIP设置、封禁用户 | 用户管理员+ | 记录用户状态变更 |
| **卡密管理** | 批量生成、导出、状态查询、作废处理 | 卡密管理员+ | 记录卡密操作 |
| **数据统计** | 内容热度、用户增长、积分消耗、收入分析 | 数据分析师+ | 查看日志 |
| **营销管理** | 邀请数据、分享转化、活动配置 | 运营人员+ | 记录营销操作 |
| **系统配置** | 首页模块UI控制、积分规则配置、系统参数配置 | 系统管理员 | 记录配置变更 |
| **权限系统** | 角色创建、菜单授权、操作日志查看 | 超级管理员 | 记录权限变更 |

### 7.2 详细功能说明

#### 内容管理
- **内容编辑器**：富文本编辑器，支持图片、视频、代码块
- **批量操作**：批量上下架、批量修改分类、批量导入
- **内容审核**：内容审核流程，支持多级审核
- **SEO管理**：标题、描述、关键词批量设置
- **数据导入**：Excel批量导入内容，支持模板下载

#### 用户管理
- **用户搜索**：支持多条件搜索用户
- **积分操作**：手动调整用户积分，支持批量操作
- **VIP管理**：VIP开通、续费、降级操作
- **用户画像**：用户行为分析、消费习惯统计
- **风控管理**：异常用户识别、批量处理

#### 积分规则配置

##### 配置功能
- **积分获取配置**：配置各种积分获取方式的数量和限制
  - 每日签到积分数量（基础/连续奖励）
  - 观看广告积分数量和每日次数限制
  - 邀请注册/解锁的积分奖励
  - 分享传播的积分奖励和次数限制
  - 内容创作的积分奖励
- **积分消耗配置**：配置内容解锁所需积分数量
  - 按内容分类设置默认积分价格
  - 支持单个内容自定义积分价格
- **特殊规则配置**：节日活动、新用户奖励等特殊积分规则
  - 新用户注册奖励积分
  - 节日活动期间积分倍数
  - VIP用户积分获取加成
- **规则模板**：预设常用的积分规则模板，快速应用
  - 新手友好模式（高积分奖励）
  - 标准模式（平衡积分经济）
  - 严格模式（低积分奖励）
- **生效时间**：支持定时生效，预设活动规则
  - 立即生效
  - 定时生效（指定日期时间）
  - 活动期间生效（开始和结束时间）

##### 配置界面功能
- **可视化配置**：表单化配置界面，实时预览效果
- **批量修改**：支持批量修改多个配置项
- **配置验证**：配置保存前进行合理性验证
- **影响评估**：显示配置修改对用户和平台的影响
- **回滚功能**：支持一键回滚到历史配置
- **导入导出**：支持配置的导入导出，便于环境迁移

#### 数据统计
- **实时数据**：用户在线数、今日新增、收入统计
- **趋势分析**：用户增长趋势、内容消费趋势
- **转化分析**：注册转化率、付费转化率
- **收入分析**：积分消耗、VIP收入、渠道分析
- **积分分析**：积分获取分布、消耗分布、积分流转分析
- **导出功能**：支持数据导出，定制报表

### 7.3 权限系统设计（RBAC）

#### 角色定义
| 角色 | 权限范围 | 典型用户 |
|------|---------|---------|
| **超级管理员** | 所有权限 | 系统负责人 |
| **系统管理员** | 系统配置、用户管理 | 技术负责人 |
| **内容管理员** | 内容管理、审核 | 内容运营 |
| **用户管理员** | 用户管理、客服 | 客服人员 |
| **数据分析师** | 数据查看、报表 | 数据分析 |
| **运营人员** | 营销活动、推广 | 运营推广 |

#### 权限控制
- **菜单权限**：控制用户可见的菜单项
- **操作权限**：控制用户可执行的操作
- **数据权限**：控制用户可访问的数据范围
- **时间权限**：控制权限的有效时间

#### 安全机制
- **登录安全**：多次失败锁定、验证码验证
- **操作审计**：所有操作记录日志，支持追溯
- **敏感操作**：重要操作需要二次确认
- **会话管理**：会话超时、强制下线功能
---

## 八、数据库核心表设计（MySQL）

### 8.1 核心表结构

| 表名 | 描述 | 主要字段 | 索引设计 |
|------|------|---------|---------|
| **users** | 用户基础信息 | id, openid, mobile, nickname, avatar, points, vip_expire_time | openid(unique), mobile(unique) |
| **contents** | 内容主表 | id, title, content, cover_image, unlock_type, unlock_price, category_id | category_id, unlock_type, created_at |
| **content_unlocks** | 内容解锁记录 | id, user_id, content_id, unlock_type, unlock_time | user_id+content_id(unique), content_id |
| **categories** | 内容分类 | id, name, parent_id, sort_order, status | parent_id, sort_order |
| **tags** | 标签表 | id, name, color, sort_order | name(unique) |
| **content_tags** | 内容标签关联 | content_id, tag_id | content_id+tag_id(unique) |
| **point_logs** | 积分变动记录 | id, user_id, type, amount, balance, description | user_id+created_at, type |
| **sign_logs** | 签到记录 | id, user_id, sign_date, points, continuous_days | user_id+sign_date(unique) |
| **ad_logs** | 广告观看记录 | id, user_id, ad_type, points, watch_time | user_id+created_at |
| **cards** | 卡密表 | id, code, type, value, batch_id, status, used_by, used_at | code(unique), batch_id, status |
| **card_batches** | 卡密批次 | id, name, type, total_count, used_count, created_by | created_by, created_at |
| **invite_logs** | 邀请记录 | id, inviter_id, invitee_id, reward_points, created_at | inviter_id, invitee_id |
| **share_logs** | 分享记录 | id, user_id, content_id, share_type, click_count | user_id, content_id, created_at |
| **comments** | 评论表 | id, content_id, user_id, parent_id, content, status | content_id, user_id, parent_id |
| **comment_likes** | 评论点赞 | id, comment_id, user_id | comment_id+user_id(unique) |
| **favorites** | 内容收藏 | id, user_id, content_id, created_at | user_id+content_id(unique) |
| **content_likes** | 内容点赞 | id, user_id, content_id, created_at | user_id+content_id(unique) |
| **admins** | 管理员用户 | id, username, password, role_id, status | username(unique) |
| **admin_roles** | 管理员角色 | id, name, permissions, description | name(unique) |
| **admin_logs** | 管理员操作日志 | id, admin_id, action, target, ip, user_agent | admin_id, created_at |
| **ui_config** | UI配置表 | id, key, value, description, updated_by | key(unique) |
| **point_config** | 积分配置表 | id, config_key, config_value, config_type, description | config_key(unique) |

### 8.2 关键表详细设计

#### users 用户表
```sql
CREATE TABLE users (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  openid VARCHAR(64) UNIQUE COMMENT '微信openid',
  mobile VARCHAR(20) UNIQUE COMMENT '手机号',
  nickname VARCHAR(50) COMMENT '昵称',
  avatar VARCHAR(255) COMMENT '头像URL',
  points INT DEFAULT 0 COMMENT '当前积分',
  total_points INT DEFAULT 0 COMMENT '累计获得积分',
  vip_level TINYINT DEFAULT 0 COMMENT 'VIP等级',
  vip_expire_time DATETIME COMMENT 'VIP到期时间',
  inviter_id BIGINT COMMENT '邀请人ID',
  invite_code VARCHAR(20) UNIQUE COMMENT '邀请码',
  status TINYINT DEFAULT 1 COMMENT '状态：1正常 2封禁',
  last_login_time DATETIME COMMENT '最后登录时间',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_openid (openid),
  INDEX idx_mobile (mobile),
  INDEX idx_invite_code (invite_code),
  INDEX idx_inviter_id (inviter_id)
);
```

#### contents 内容表
```sql
CREATE TABLE contents (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  title VARCHAR(200) NOT NULL COMMENT '标题',
  summary TEXT COMMENT '摘要',
  content LONGTEXT COMMENT '内容',
  cover_image VARCHAR(255) COMMENT '封面图',
  category_id INT COMMENT '分类ID',
  unlock_type TINYINT DEFAULT 1 COMMENT '解锁方式：1积分 2VIP 3卡密 4免费',
  unlock_price INT DEFAULT 0 COMMENT '解锁价格（积分）',
  view_count INT DEFAULT 0 COMMENT '浏览次数',
  like_count INT DEFAULT 0 COMMENT '点赞次数',
  unlock_count INT DEFAULT 0 COMMENT '解锁次数',
  share_count INT DEFAULT 0 COMMENT '分享次数',
  status TINYINT DEFAULT 1 COMMENT '状态：1正常 2下架',
  is_recommend TINYINT DEFAULT 0 COMMENT '是否推荐',
  sort_order INT DEFAULT 0 COMMENT '排序权重',
  created_by BIGINT COMMENT '创建人',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_category_id (category_id),
  INDEX idx_unlock_type (unlock_type),
  INDEX idx_status (status),
  INDEX idx_created_at (created_at),
  INDEX idx_recommend (is_recommend, sort_order)
);
```

#### point_config 积分配置表
```sql
CREATE TABLE point_config (
  id INT PRIMARY KEY AUTO_INCREMENT,
  config_key VARCHAR(50) UNIQUE NOT NULL COMMENT '配置键名',
  config_value VARCHAR(500) NOT NULL COMMENT '配置值（JSON格式）',
  config_type VARCHAR(20) NOT NULL COMMENT '配置类型：gain获取/consume消耗/limit限制',
  description VARCHAR(200) COMMENT '配置说明',
  is_active TINYINT DEFAULT 1 COMMENT '是否启用',
  created_by BIGINT COMMENT '创建人',
  updated_by BIGINT COMMENT '更新人',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_config_type (config_type),
  INDEX idx_is_active (is_active)
);

-- 默认积分配置数据
INSERT INTO point_config (config_key, config_value, config_type, description) VALUES
('daily_sign_base', '{"points": 5, "max_per_day": 1}', 'gain', '每日签到基础积分'),
('daily_sign_continuous_7', '{"points": 10, "condition": "continuous_7_days"}', 'gain', '连续签到7天奖励'),
('daily_sign_continuous_30', '{"points": 20, "condition": "continuous_30_days"}', 'gain', '连续签到30天奖励'),
('watch_ad', '{"points": 3, "max_per_day": 5}', 'gain', '观看广告积分'),
('invite_register', '{"points": 20, "max_per_day": -1}', 'gain', '邀请注册积分'),
('invite_unlock_ratio', '{"ratio": 0.2}', 'gain', '邀请解锁分成比例'),
('share_content', '{"points": 2, "max_per_day": 10}', 'gain', '分享内容积分'),
('content_creation', '{"points": 100, "max_per_day": -1}', 'gain', '内容创作积分'),
('complete_profile', '{"points": 10, "max_times": 1}', 'gain', '完善资料积分'),
('first_unlock', '{"points": 5, "max_times": 1}', 'gain', '首次解锁奖励'),
('point_expire_days', '{"days": 365}', 'limit', '积分有效期（天）'),
('daily_gain_limit', '{"max_points": 200}', 'limit', '每日获取积分上限');

-- 积分配置示例JSON格式说明
/*
签到配置示例：
{
  "points": 5,           // 基础积分
  "max_per_day": 1,      // 每日最大次数
  "continuous_bonus": {   // 连续签到奖励
    "7": 10,             // 连续7天额外奖励10积分
    "30": 20             // 连续30天额外奖励20积分
  }
}

观看广告配置示例：
{
  "points": 3,           // 每次观看积分
  "max_per_day": 5,      // 每日最大观看次数
  "cooldown": 300        // 冷却时间（秒）
}

邀请配置示例：
{
  "register_reward": 20,  // 邀请注册奖励
  "unlock_ratio": 0.2,    // 邀请解锁分成比例
  "max_per_day": -1,      // 每日邀请限制（-1表示无限制）
  "level_bonus": {        // VIP等级加成
    "1": 1.2,             // VIP1用户邀请奖励1.2倍
    "2": 1.5              // VIP2用户邀请奖励1.5倍
  }
}
*/
```

### 8.3 数据库优化策略

#### 性能优化
- **读写分离**：主库写入，从库读取
- **分库分表**：用户表按用户ID分表，日志表按时间分表
- **缓存策略**：热点数据Redis缓存，减少数据库压力
- **索引优化**：合理设计索引，避免全表扫描

#### 数据备份
- **定时备份**：每日全量备份，每小时增量备份
- **异地备份**：多地域备份，保证数据安全
- **备份验证**：定期验证备份数据完整性
- **恢复演练**：定期进行数据恢复演练

---

## 九、安全与性能

### 9.1 安全策略

#### 数据安全
- **数据加密**：敏感数据加密存储
- **传输安全**：HTTPS传输，API签名验证
- **访问控制**：IP白名单，访问频率限制
- **数据脱敏**：日志中敏感信息脱敏处理

#### 业务安全
- **防刷机制**：积分获取、内容解锁防刷
- **风控系统**：异常行为监控和处理
- **内容审核**：敏感内容过滤和人工审核
- **用户举报**：不良内容举报处理机制

### 9.2 性能优化

#### 前端优化
- **资源压缩**：JS、CSS、图片压缩
- **CDN加速**：静态资源CDN分发
- **懒加载**：图片和内容懒加载
- **缓存策略**：浏览器缓存、应用缓存

#### 后端优化
- **接口优化**：减少数据库查询，优化SQL
- **缓存策略**：Redis缓存热点数据
- **异步处理**：耗时操作异步处理
- **负载均衡**：多实例部署，负载均衡

---

## 十、项目实施计划

### 10.1 开发阶段

| 阶段 | 时间 | 主要任务 | 交付物 |
|------|------|---------|--------|
| **需求分析** | 1周 | 需求梳理、原型设计 | 需求文档、原型图 |
| **技术设计** | 1周 | 技术方案、数据库设计 | 技术方案、数据库设计 |
| **基础开发** | 3周 | 用户系统、内容系统 | 基础功能模块 |
| **核心功能** | 4周 | 积分系统、解锁系统 | 核心业务功能 |
| **高级功能** | 3周 | 推荐系统、分享系统 | 高级功能模块 |
| **管理后台** | 2周 | 后台管理系统 | 管理后台 |
| **测试优化** | 2周 | 功能测试、性能优化 | 测试报告 |
| **上线部署** | 1周 | 生产环境部署 | 线上系统 |

### 10.2 风险控制

#### 技术风险
- **技术选型风险**：选择成熟稳定的技术栈
- **性能风险**：提前进行性能测试和优化
- **安全风险**：建立完善的安全防护机制

#### 业务风险
- **需求变更风险**：建立需求变更管理流程
- **用户接受度风险**：小范围内测，收集用户反馈
- **竞争风险**：持续关注竞品，保持产品优势

---

## 十一、运营推广策略

### 11.1 用户获取

#### 推广渠道
- **微信生态**：公众号推广、微信群分享
- **社交媒体**：抖音、小红书等平台推广
- **合作推广**：与教育机构、KOL合作
- **SEO优化**：搜索引擎优化，提升自然流量

#### 激励机制
- **新用户奖励**：注册送积分、首次解锁优惠
- **邀请奖励**：邀请好友获得积分奖励
- **活动营销**：节日活动、限时优惠
- **内容激励**：优质内容创作者奖励

### 11.2 用户留存

#### 产品策略
- **内容质量**：持续提供高质量内容
- **用户体验**：优化产品体验，降低使用门槛
- **个性化推荐**：提升内容推荐精准度
- **社区建设**：建立用户社区，增强用户粘性

#### 运营策略
- **用户分层**：不同用户群体差异化运营
- **生命周期管理**：用户全生命周期运营
- **数据驱动**：基于数据分析优化运营策略
- **用户反馈**：及时响应用户反馈，持续改进

