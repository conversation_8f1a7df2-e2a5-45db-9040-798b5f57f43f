const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const CardBatch = sequelize.define('CardBatch', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
      comment: '批次ID'
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: '批次名称'
    },
    type: {
      type: DataTypes.ENUM('points', 'vip', 'content'),
      allowNull: false,
      comment: '卡密类型'
    },
    totalCount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '总数量'
    },
    usedCount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '已使用数量'
    },
    createdBy: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '创建人'
    },
    remark: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '备注'
    }
  }, {
    tableName: 'card_batches',
    comment: '卡密批次表',
    indexes: [
      {
        fields: ['created_by']
      },
      {
        fields: ['type']
      },
      {
        fields: ['created_at']
      }
    ]
  });

  return CardBatch;
};
