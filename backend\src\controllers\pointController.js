const { User, PointLog, SignLog, <PERSON>L<PERSON>, Card, PointConfig } = require('../models');
const logger = require('../utils/logger');
const { BusinessError, ValidationError } = require('../middleware/errorHandler');

class PointController {
  // 每日签到
  async dailySign(ctx) {
    const userId = ctx.state.user.id;
    const today = new Date().toISOString().split('T')[0];

    // 检查今日是否已签到
    const existingSign = await SignLog.findOne({
      where: {
        userId,
        signDate: today
      }
    });

    if (existingSign) {
      throw new BusinessError('今日已签到');
    }

    // 获取签到配置
    const signConfig = await PointConfig.getByKey('daily_sign_base');
    if (!signConfig) {
      throw new BusinessError('签到功能暂未开放');
    }

    // 计算连续签到天数
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const yesterdayStr = yesterday.toISOString().split('T')[0];

    const lastSign = await SignLog.findOne({
      where: {
        userId,
        signDate: yesterdayStr
      }
    });

    let continuousDays = 1;
    if (lastSign) {
      continuousDays = lastSign.continuousDays + 1;
    }

    // 计算签到奖励积分
    let points = signConfig.points || 5;
    
    // 连续签到奖励
    if (continuousDays >= 30) {
      const bonus30Config = await PointConfig.getByKey('daily_sign_continuous_30');
      if (bonus30Config) {
        points += bonus30Config.points || 20;
      }
    } else if (continuousDays >= 7) {
      const bonus7Config = await PointConfig.getByKey('daily_sign_continuous_7');
      if (bonus7Config) {
        points += bonus7Config.points || 10;
      }
    }

    // 获取用户
    const user = await User.findByPk(userId);

    // 添加积分
    await user.addPoints(points, `每日签到奖励（连续${continuousDays}天）`);

    // 记录签到日志
    await SignLog.create({
      userId,
      signDate: today,
      points,
      continuousDays,
      ip: ctx.ip
    });

    logger.business('每日签到', userId, { 
      points, 
      continuousDays,
      ip: ctx.ip 
    });

    ctx.body = {
      code: 200,
      message: '签到成功',
      data: {
        points,
        continuousDays,
        totalPoints: user.points + points
      }
    };
  }

  // 观看广告
  async watchAd(ctx) {
    const userId = ctx.state.user.id;
    const { adType = 'reward', watchTime = 30 } = ctx.request.body;

    // 获取广告配置
    const adConfig = await PointConfig.getByKey('watch_ad');
    if (!adConfig) {
      throw new BusinessError('广告功能暂未开放');
    }

    // 检查今日观看次数
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const todayCount = await AdLog.count({
      where: {
        userId,
        createdAt: {
          [require('sequelize').Op.gte]: today,
          [require('sequelize').Op.lt]: tomorrow
        }
      }
    });

    const maxPerDay = adConfig.maxPerDay || 5;
    if (todayCount >= maxPerDay) {
      throw new BusinessError(`今日观看次数已达上限（${maxPerDay}次）`);
    }

    const points = adConfig.points || 3;

    // 获取用户并添加积分
    const user = await User.findByPk(userId);
    await user.addPoints(points, `观看${adType}广告奖励`);

    // 记录广告日志
    await AdLog.create({
      userId,
      adType,
      points,
      watchTime,
      ip: ctx.ip
    });

    logger.business('观看广告', userId, { 
      adType, 
      points, 
      watchTime,
      ip: ctx.ip 
    });

    ctx.body = {
      code: 200,
      message: '观看广告成功',
      data: {
        points,
        remainingCount: maxPerDay - todayCount - 1,
        totalPoints: user.points + points
      }
    };
  }

  // 卡密兑换
  async cardExchange(ctx) {
    const userId = ctx.state.user.id;
    const { cardCode } = ctx.request.body;

    if (!cardCode) {
      throw new ValidationError('卡密不能为空');
    }

    // 查找卡密
    const card = await Card.findOne({
      where: { code: cardCode }
    });

    if (!card) {
      throw new BusinessError('卡密不存在');
    }

    if (card.status !== 1) {
      throw new BusinessError('卡密已使用或已失效');
    }

    // 检查是否过期
    if (card.expireAt && new Date() > card.expireAt) {
      throw new BusinessError('卡密已过期');
    }

    const user = await User.findByPk(userId);
    const cardValue = JSON.parse(card.value);

    // 根据卡密类型处理
    if (card.type === 'points') {
      // 积分卡
      const points = cardValue.points || 0;
      await user.addPoints(points, `卡密兑换：${cardCode}`);
      
      logger.business('卡密兑换', userId, { 
        cardCode, 
        type: 'points', 
        points 
      });
    } else if (card.type === 'vip') {
      // VIP卡
      const days = cardValue.days || 30;
      const level = cardValue.level || 1;
      
      const expireTime = new Date();
      if (user.vipExpireTime && user.vipExpireTime > new Date()) {
        expireTime.setTime(user.vipExpireTime.getTime());
      }
      expireTime.setDate(expireTime.getDate() + days);
      
      user.vipLevel = Math.max(user.vipLevel, level);
      user.vipExpireTime = expireTime;
      await user.save();
      
      logger.business('卡密兑换', userId, { 
        cardCode, 
        type: 'vip', 
        level, 
        days 
      });
    }

    // 更新卡密状态
    card.status = 2; // 已使用
    card.usedBy = userId;
    card.usedAt = new Date();
    await card.save();

    ctx.body = {
      code: 200,
      message: '兑换成功',
      data: {
        cardType: card.type,
        cardValue,
        user: user.toJSON()
      }
    };
  }

  // 获取积分日志
  async getLogs(ctx) {
    const userId = ctx.state.user.id;
    const { page = 1, pageSize = 20, type } = ctx.query;

    const result = await PointLog.getUserLogs(userId, parseInt(page), parseInt(pageSize), type);

    ctx.body = {
      code: 200,
      message: '获取成功',
      data: {
        list: result.rows,
        total: result.count,
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      }
    };
  }

  // 获取积分统计
  async getStats(ctx) {
    const userId = ctx.state.user.id;
    const { days = 30 } = ctx.query;

    const stats = await PointLog.getUserStats(userId, parseInt(days));

    ctx.body = {
      code: 200,
      message: '获取成功',
      data: stats
    };
  }

  // 获取积分配置
  async getConfig(ctx) {
    const gainConfigs = await PointConfig.getGainConfigs();
    const limitConfigs = await PointConfig.getLimitConfigs();

    ctx.body = {
      code: 200,
      message: '获取成功',
      data: {
        gain: gainConfigs,
        limit: limitConfigs
      }
    };
  }

  // 获取今日积分获取情况
  async getTodayStats(ctx) {
    const userId = ctx.state.user.id;
    const stats = await PointLog.getTodayGainStats(userId);

    ctx.body = {
      code: 200,
      message: '获取成功',
      data: stats
    };
  }

  // 获取积分来源统计
  async getSourceStats(ctx) {
    const userId = ctx.state.user.id;
    const { days = 30 } = ctx.query;

    const stats = await PointLog.getSourceStats(userId, parseInt(days));

    ctx.body = {
      code: 200,
      message: '获取成功',
      data: stats
    };
  }
}

module.exports = new PointController();
